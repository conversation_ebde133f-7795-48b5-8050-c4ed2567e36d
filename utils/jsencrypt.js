import JSEncrypt from 'jsencrypt';
// 密钥对生成 http://web.chacuo.net/netrsakeypair

const publicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCJnKUJNSZnEuSc3nF76FYpPxf4SCCLUujv0Kmrgobj/MK3YaEczMYO/oP2Zgb8C05W+ofvMrfvXahfOxHKhwcBsRsvqC5PIuJUoO5P6qWmhqoUZqazlrZq1tpkjUvIujMFcz/gZML0/41Ky+wKcF+r3By5PgM1zmIukR9iRwn3ZQIDAQAB';

// 前端不建议存放私钥 不建议解密数据 因为都是透明的意义不大
const privateKey = '**********';

// 加密
export function encrypt(txt){
  const encryptor = new JSEncrypt();
  encryptor.setPublicKey(publicKey); // 设置公钥
  return encryptor.encrypt(txt); // 对数据进行加密
};

// 解密
export function decrypt(txt){
  const encryptor = new JSEncrypt();
  encryptor.setPrivateKey(privateKey); // 设置私钥
  return encryptor.decrypt(txt); // 对数据进行解密
};
