<template>
  <view class="datetime-picker-fix">
    <view class="picker-input" @click="openPicker">
      <text class="picker-text" :class="{ 'placeholder': !displayValue }">
        {{ displayValue || placeholder }}
      </text>
      <text class="picker-icon">📅</text>
    </view>
    
    <!-- 使用原生picker组件确保兼容性 -->
    <picker 
      v-if="showPicker"
      mode="multiSelector" 
      :value="pickerValue" 
      :range="pickerRange"
      @change="onPickerChange"
      @cancel="closePicker"
      class="hidden-picker"
    >
      <view></view>
    </picker>
    
    <!-- 自定义弹窗 -->
    <view v-if="showPicker" class="picker-overlay" @click="closePicker">
      <view class="picker-container" @click.stop>
        <view class="picker-header">
          <text class="picker-cancel" @click="closePicker">取消</text>
          <text class="picker-title">{{ title }}</text>
          <text class="picker-confirm" @click="confirmSelection">确定</text>
        </view>
        
        <picker-view 
          :value="pickerValue" 
          @change="onPickerViewChange"
          class="picker-view-container"
        >
          <!-- 年 -->
          <picker-view-column>
            <view v-for="(item, index) in pickerRange[0]" :key="index" class="picker-item">
              {{ item }}
            </view>
          </picker-view-column>
          
          <!-- 月 -->
          <picker-view-column>
            <view v-for="(item, index) in pickerRange[1]" :key="index" class="picker-item">
              {{ item }}
            </view>
          </picker-view-column>
          
          <!-- 日 -->
          <picker-view-column>
            <view v-for="(item, index) in pickerRange[2]" :key="index" class="picker-item">
              {{ item }}
            </view>
          </picker-view-column>
          
          <!-- 时 -->
          <picker-view-column v-if="type === 'datetime'">
            <view v-for="(item, index) in pickerRange[3]" :key="index" class="picker-item">
              {{ item }}
            </view>
          </picker-view-column>
          
          <!-- 分 -->
          <picker-view-column v-if="type === 'datetime'">
            <view v-for="(item, index) in pickerRange[4]" :key="index" class="picker-item">
              {{ item }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'DatetimePickerFix',
  props: {
    value: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'datetime'
    },
    placeholder: {
      type: String,
      default: '请选择日期时间'
    },
    title: {
      type: String,
      default: '选择日期时间'
    }
  },
  data() {
    return {
      showPicker: false,
      pickerValue: [0, 0, 0, 0, 0],
      pickerRange: [[], [], [], [], []]
    }
  },
  computed: {
    displayValue() {
      if (!this.value) return ''
      
      try {
        const date = new Date(this.value)
        if (isNaN(date.getTime())) return ''
        
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        
        if (this.type === 'datetime') {
          const hour = String(date.getHours()).padStart(2, '0')
          const minute = String(date.getMinutes()).padStart(2, '0')
          return `${year}-${month}-${day} ${hour}:${minute}`
        } else {
          return `${year}-${month}-${day}`
        }
      } catch (e) {
        return ''
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.initPickerValue()
      },
      immediate: true
    }
  },
  mounted() {
    this.initPickerRange()
    this.initPickerValue()
  },
  methods: {
    initPickerRange() {
      // 年份范围（当前年份前后10年）
      const currentYear = new Date().getFullYear()
      const years = []
      for (let i = currentYear - 10; i <= currentYear + 10; i++) {
        years.push(i + '年')
      }
      
      // 月份范围
      const months = []
      for (let i = 1; i <= 12; i++) {
        months.push(i + '月')
      }
      
      // 日期范围（动态计算）
      const days = this.getDaysInMonth(currentYear, new Date().getMonth() + 1)
      
      // 小时范围
      const hours = []
      for (let i = 0; i <= 23; i++) {
        hours.push(String(i).padStart(2, '0') + '时')
      }
      
      // 分钟范围
      const minutes = []
      for (let i = 0; i <= 59; i++) {
        minutes.push(String(i).padStart(2, '0') + '分')
      }
      
      this.pickerRange = [years, months, days, hours, minutes]
    },
    
    getDaysInMonth(year, month) {
      const daysInMonth = new Date(year, month, 0).getDate()
      const days = []
      for (let i = 1; i <= daysInMonth; i++) {
        days.push(i + '日')
      }
      return days
    },
    
    initPickerValue() {
      if (this.value) {
        try {
          const date = new Date(this.value)
          if (isNaN(date.getTime())) {
            this.setCurrentTime()
            return
          }
          
          const year = date.getFullYear()
          const month = date.getMonth() + 1
          const day = date.getDate()
          const hour = date.getHours()
          const minute = date.getMinutes()
          
          // 找到对应的索引
          const yearIndex = this.pickerRange[0].findIndex(item => item === year + '年')
          const monthIndex = month - 1
          const dayIndex = day - 1
          const hourIndex = hour
          const minuteIndex = minute
          
          this.pickerValue = [
            yearIndex >= 0 ? yearIndex : 10, // 默认当前年份
            monthIndex,
            dayIndex,
            hourIndex,
            minuteIndex
          ]
          
          // 更新日期范围
          this.updateDaysRange()
        } catch (e) {
          this.setCurrentTime()
        }
      } else {
        this.setCurrentTime()
      }
    },
    
    setCurrentTime() {
      const now = new Date()
      const currentYear = now.getFullYear()
      const yearIndex = this.pickerRange[0].findIndex(item => item === currentYear + '年')
      
      this.pickerValue = [
        yearIndex >= 0 ? yearIndex : 10,
        now.getMonth(),
        now.getDate() - 1,
        now.getHours(),
        now.getMinutes()
      ]
    },
    
    updateDaysRange() {
      const yearStr = this.pickerRange[0][this.pickerValue[0]]
      const monthStr = this.pickerRange[1][this.pickerValue[1]]
      
      if (yearStr && monthStr) {
        const year = parseInt(yearStr.replace('年', ''))
        const month = parseInt(monthStr.replace('月', ''))
        
        const newDays = this.getDaysInMonth(year, month)
        this.pickerRange[2] = newDays
        
        // 如果当前选择的日期超出了新月份的天数，调整到最后一天
        if (this.pickerValue[2] >= newDays.length) {
          this.pickerValue[2] = newDays.length - 1
        }
      }
    },
    
    onPickerViewChange(e) {
      const oldValue = [...this.pickerValue]
      this.pickerValue = e.detail.value
      
      // 如果年份或月份发生变化，更新日期范围
      if (oldValue[0] !== this.pickerValue[0] || oldValue[1] !== this.pickerValue[1]) {
        this.updateDaysRange()
      }
    },
    
    onPickerChange(e) {
      this.pickerValue = e.detail.value
      this.confirmSelection()
    },
    
    openPicker() {
      this.initPickerValue()
      this.showPicker = true
      this.$emit('open')
    },
    
    closePicker() {
      this.showPicker = false
      this.$emit('close')
    },
    
    confirmSelection() {
      const yearStr = this.pickerRange[0][this.pickerValue[0]]
      const monthStr = this.pickerRange[1][this.pickerValue[1]]
      const dayStr = this.pickerRange[2][this.pickerValue[2]]
      
      if (!yearStr || !monthStr || !dayStr) {
        this.closePicker()
        return
      }
      
      const year = parseInt(yearStr.replace('年', ''))
      const month = parseInt(monthStr.replace('月', ''))
      const day = parseInt(dayStr.replace('日', ''))
      
      let dateString = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
      
      if (this.type === 'datetime') {
        const hourStr = this.pickerRange[3][this.pickerValue[3]]
        const minuteStr = this.pickerRange[4][this.pickerValue[4]]
        
        if (hourStr && minuteStr) {
          const hour = parseInt(hourStr.replace('时', ''))
          const minute = parseInt(minuteStr.replace('分', ''))
          dateString += ` ${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}:00`
        }
      }
      
      this.$emit('input', dateString)
      this.$emit('change', dateString)
      this.closePicker()
    }
  }
}
</script>

<style lang="scss" scoped>
.datetime-picker-fix {
  width: 100%;
}

.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  height: 70rpx;
  background-color: #f8f9fc;
  border-radius: 8rpx;
  border: 1px solid #e5e7eb;
  
  .picker-text {
    flex: 1;
    font-size: 28rpx;
    color: #333;
    
    &.placeholder {
      color: #999;
    }
  }
  
  .picker-icon {
    font-size: 28rpx;
    color: #666;
  }
}

.hidden-picker {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: flex-end;
}

.picker-container {
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1px solid #f0f0f0;
  
  .picker-cancel,
  .picker-confirm {
    font-size: 28rpx;
    color: #007AFF;
    padding: 10rpx;
  }
  
  .picker-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }
}

.picker-view-container {
  height: 400rpx;
  padding: 20rpx 0;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
}
</style>
