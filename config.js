// 应用全局配置
module.exports = {

	//生产打包需要改成这个
	// baseUrl:'https://**************/prod-api',//三云
	// baseUrl:'https://*************/prod-api',//阿里云

	//本机启动的话改成这个
	baseUrl: 'http://localhost:8080',

	//app账号密码默认传这个
	itsmClientId: '428a8310cd442757ae699df5d894f051',
	
	//接口加密功能开关(如需关闭 后端也必须对应关闭)
	VITE_APP_ENCRYPT : true,

	//授权模式
	grantType:'password',

	//记住我模式
	rememberMe: false,

	// 应用信息
	appInfo: {
		// 应用名称
		name: "ruoyi-app",
		// 应用版本
		version: "1.1.0",
		// 应用logo
		logo: "/static/logo.png",
		// 官方网站
		site_url: "http://ruoyi.vip",
		// 政策协议
		agreements: [{
				title: "隐私政策",
				url: "https://ruoyi.vip/protocol.html"
			},
			{
				title: "用户服务协议",
				url: "https://ruoyi.vip/protocol.html"
			}
		]
	}
}

