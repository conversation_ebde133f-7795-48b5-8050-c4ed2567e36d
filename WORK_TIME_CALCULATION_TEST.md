# 请假时长计算测试用例

## 🕐 工作时间制度

### 工作时间安排
- **上午**：8:30-12:00（3.5小时）
- **午休**：12:00-13:30（1.5小时休息）
- **下午**：13:30-18:00（4.5小时）
- **每日工作时间**：8小时（3.5 + 4.5）
- **工作日**：周一至周五

### 配置参数
```javascript
workTimeConfig: {
  morningStart: { hour: 8, minute: 30 },   // 上午开始：8:30
  morningEnd: { hour: 12, minute: 0 },     // 上午结束：12:00
  afternoonStart: { hour: 13, minute: 30 }, // 下午开始：13:30
  afternoonEnd: { hour: 18, minute: 0 },   // 下午结束：18:00
  workHoursPerDay: 8,  // 每天工作小时数
  workDays: [1, 2, 3, 4, 5], // 工作日：周一到周五
}
```

## 📊 测试用例

### 测试用例1：同一天上午请假
**请假时间**：2025-06-06（周五）09:00 至 11:00
**计算过程**：
- 上午时段：09:00-11:00 = 2小时
- 下午时段：无
**预期结果**：2小时

### 测试用例2：同一天跨午休请假
**请假时间**：2025-06-06（周五）11:00 至 15:00
**计算过程**：
- 上午时段：11:00-12:00 = 1小时
- 午休时段：12:00-13:30（不计入工作时间）
- 下午时段：13:30-15:00 = 1.5小时
**预期结果**：2小时30分钟

### 测试用例3：同一天全天请假
**请假时间**：2025-06-06（周五）08:30 至 18:00
**计算过程**：
- 上午时段：08:30-12:00 = 3.5小时
- 下午时段：13:30-18:00 = 4.5小时
**预期结果**：1天（8小时）

### 测试用例4：您的示例（跨天请假）
**请假时间**：2025-06-06（周五）16:17 至 2025-06-09（周一）16:17
**计算过程**：
- **6月6日（周五）**：
  - 下午时段：16:17-18:00 = 1小时43分钟
- **6月7日（周六）**：非工作日 = 0小时
- **6月8日（周日）**：非工作日 = 0小时
- **6月9日（周一）**：
  - 上午时段：08:30-12:00 = 3.5小时
  - 下午时段：13:30-16:17 = 2小时47分钟
**总计**：1小时43分钟 + 3.5小时 + 2小时47分钟 = 8小时
**预期结果**：1天

### 测试用例5：跨工作周请假
**请假时间**：2025-06-06（周五）14:00 至 2025-06-10（周二）10:00
**计算过程**：
- **6月6日（周五）**：14:00-18:00 = 4小时
- **6月7日（周六）**：非工作日 = 0小时
- **6月8日（周日）**：非工作日 = 0小时
- **6月9日（周一）**：完整工作日 = 8小时
- **6月10日（周二）**：08:30-10:00 = 1.5小时
**总计**：4 + 8 + 1.5 = 13.5小时
**预期结果**：1天5小时30分钟

### 测试用例6：午休时间请假
**请假时间**：2025-06-06（周五）12:00 至 13:30
**计算过程**：
- 午休时段：12:00-13:30（不计入工作时间）
**预期结果**：0小时

### 测试用例7：非工作时间请假
**请假时间**：2025-06-06（周五）19:00 至 21:00
**计算过程**：
- 非工作时间：19:00-21:00（不计入工作时间）
**预期结果**：0小时

### 测试用例8：周末请假
**请假时间**：2025-06-07（周六）09:00 至 2025-06-08（周日）17:00
**计算过程**：
- 6月7日（周六）：非工作日 = 0小时
- 6月8日（周日）：非工作日 = 0小时
**预期结果**：0小时

## 🔍 边界情况测试

### 边界1：工作时间边界
**请假时间**：2025-06-06（周五）08:29 至 08:31
**计算过程**：
- 08:29-08:30：非工作时间
- 08:30-08:31：工作时间 = 1分钟
**预期结果**：1分钟

### 边界2：午休边界
**请假时间**：2025-06-06（周五）11:59 至 13:31
**计算过程**：
- 11:59-12:00：上午工作时间 = 1分钟
- 12:00-13:30：午休时间（不计入）
- 13:30-13:31：下午工作时间 = 1分钟
**预期结果**：2分钟

### 边界3：跨周末最小请假
**请假时间**：2025-06-06（周五）17:59 至 2025-06-09（周一）08:31
**计算过程**：
- 6月6日：17:59-18:00 = 1分钟
- 6月7-8日：周末 = 0小时
- 6月9日：08:30-08:31 = 1分钟
**预期结果**：2分钟

## 🎯 显示格式测试

### 格式1：分钟显示
- 输入：30分钟
- 显示：30分钟

### 格式2：小时+分钟显示
- 输入：3.5小时
- 显示：3小时30分钟

### 格式3：天+小时+分钟显示
- 输入：10.75小时
- 显示：1天2小时45分钟

### 格式4：整天显示
- 输入：16小时
- 显示：2天

## 🛠️ 算法验证

### 上午时段计算
```javascript
// 请假时间：09:00-11:00
morningLeaveStart = max(09:00, 08:30) = 09:00
morningLeaveEnd = min(11:00, 12:00) = 11:00
morningHours = (11:00 - 09:00) = 2小时
```

### 下午时段计算
```javascript
// 请假时间：14:00-16:00
afternoonLeaveStart = max(14:00, 13:30) = 14:00
afternoonLeaveEnd = min(16:00, 18:00) = 16:00
afternoonHours = (16:00 - 14:00) = 2小时
```

### 跨时段计算
```javascript
// 请假时间：11:00-15:00
上午：max(11:00, 08:30) 到 min(15:00, 12:00) = 11:00-12:00 = 1小时
下午：max(11:00, 13:30) 到 min(15:00, 18:00) = 13:30-15:00 = 1.5小时
总计：1 + 1.5 = 2.5小时
```

## ✅ 验证清单

### 功能验证
- [ ] 同一天上午请假计算正确
- [ ] 同一天下午请假计算正确
- [ ] 跨午休请假计算正确
- [ ] 跨天请假计算正确
- [ ] 跨周末请假计算正确
- [ ] 非工作日请假为0
- [ ] 非工作时间请假为0

### 边界验证
- [ ] 工作时间边界处理正确
- [ ] 午休时间边界处理正确
- [ ] 周末边界处理正确
- [ ] 最小时间单位（分钟）处理正确

### 显示验证
- [ ] 分钟格式显示正确
- [ ] 小时+分钟格式显示正确
- [ ] 天+小时+分钟格式显示正确
- [ ] 工作时间显示格式正确

### 性能验证
- [ ] 计算响应时间 < 100ms
- [ ] 大时间跨度计算正确
- [ ] 内存使用合理

## 🚀 预期改进效果

通过这次修正，请假时长计算将：
1. **精确反映实际工作时间**：严格按照8:30-12:00, 13:30-18:00计算
2. **正确处理午休时间**：12:00-13:30不计入工作时间
3. **准确跨天计算**：正确处理周末和跨天情况
4. **友好显示格式**：清晰的时长显示和工作时间说明

您的示例（6月6日16:17到6月9日16:17）应该显示为"1天"，完全符合8小时工作制的计算要求。
