# 自定义日期时间选择器组件

## 问题背景

原生的 `uni-datetime-picker` 组件在移动端存在以下问题：

1. **显示异常**：日历弹窗在移动端浏览器和APP中显示不正确
2. **层级冲突**：弹窗的z-index可能与页面其他元素冲突
3. **定位问题**：受到scroll-view等容器影响，定位不准确
4. **移动端适配**：PC端和移动端判断逻辑不够准确

## 解决方案

### 技术特性

1. **移动端优化**：专门为移动端设计的底部弹窗样式
2. **高层级显示**：使用 `z-index: 10000` 确保在最顶层显示
3. **原生组件**：使用 `picker-view` 确保跨平台兼容性
4. **动态计算**：正确处理不同月份天数，包括闰年
5. **平滑动画**：添加淡入和滑入动画效果

### 核心修复

- ✅ 解决日历显示异常问题
- ✅ 修复层级冲突导致的显示问题
- ✅ 优化移动端用户体验
- ✅ 确保跨平台兼容性
- ✅ 添加详细的代码注释

## 使用方法

### 1. 基本用法

```vue
<template>
  <view>
    <DatetimePickerFix
      v-model="selectedDate"
      type="datetime"
      placeholder="请选择日期时间"
      title="选择日期时间"
      @change="onDateChange"
    />
  </view>
</template>

<script>
import DatetimePickerFix from '@/components/datetime-picker-fix/datetime-picker-fix.vue'

export default {
  components: {
    DatetimePickerFix
  },
  data() {
    return {
      selectedDate: ''
    }
  },
  methods: {
    onDateChange(value) {
      console.log('选择的日期时间:', value)
    }
  }
}
</script>
```

### 2. 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| value / v-model | String | '' | 绑定值，格式：YYYY-MM-DD HH:mm:ss |
| type | String | 'datetime' | 选择器类型：'date' 或 'datetime' |
| placeholder | String | '请选择日期时间' | 占位符文本 |
| title | String | '选择日期时间' | 弹窗标题 |

### 3. 事件说明

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| change | 值改变时触发 | (value: string) |
| input | 输入时触发（用于v-model） | (value: string) |
| open | 打开选择器时触发 | - |
| close | 关闭选择器时触发 | - |

### 4. 返回值格式

- **datetime 模式**：`YYYY-MM-DD HH:mm:ss`（如：2025-06-06 14:30:00）
- **date 模式**：`YYYY-MM-DD`（如：2025-06-06）

## 代码结构

```
datetime-picker-fix/
├── datetime-picker-fix.vue    # 主组件文件
└── README.md                  # 说明文档
```

## 关键实现

### 1. 移动端弹窗设计

```scss
.picker-overlay {
  position: fixed;
  z-index: 10000;           // 高层级确保显示
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;    // 底部对齐
}
```

### 2. 动态日期计算

```javascript
getDaysInMonth(year, month) {
  // 正确处理闰年和不同月份的天数
  const daysInMonth = new Date(year, month, 0).getDate()
  const days = []
  for (let i = 1; i <= daysInMonth; i++) {
    days.push(i + '日')
  }
  return days
}
```

### 3. 年月变化监听

```javascript
onPickerViewChange(e) {
  const oldValue = [...this.pickerValue]
  this.pickerValue = e.detail.value
  
  // 年份或月份变化时重新计算天数
  if (oldValue[0] !== this.pickerValue[0] || oldValue[1] !== this.pickerValue[1]) {
    this.updateDaysRange()
  }
}
```

## 兼容性

- ✅ uni-app 全平台支持
- ✅ H5 浏览器
- ✅ 微信小程序
- ✅ APP（iOS/Android）
- ✅ 其他小程序平台

## 注意事项

1. 确保项目中已安装 uni-app 相关依赖
2. 组件使用了 `picker-view` 原生组件，确保平台支持
3. 样式使用了 `rpx` 单位，适配不同屏幕尺寸
4. 建议在表单验证中检查日期格式的有效性

## 更新日志

### v1.0.0 (2025-06-06)
- 🎉 初始版本发布
- ✨ 支持日期和日期时间选择
- 🐛 修复原生组件在移动端的显示问题
- 💄 优化移动端用户体验
- 📝 添加详细的代码注释
