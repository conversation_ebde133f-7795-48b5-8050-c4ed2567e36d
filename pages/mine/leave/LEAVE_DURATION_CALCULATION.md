# 请假时长计算逻辑说明

## 📋 需求分析

### 业务需求
- **8小时工作制**：每天标准工作时间为8小时
- **工作时间**：9:00-17:00（可配置）
- **工作日**：周一至周五（可配置）
- **精确计算**：按实际工作时间计算，不包含非工作时间

### 计算规则
1. **同一天请假**：计算当天工作时间内的请假时长
2. **跨天请假**：
   - 开始日：从请假开始时间到当天工作结束时间
   - 中间日：完整工作日按8小时计算
   - 结束日：从工作开始时间到请假结束时间
3. **非工作日**：周末和节假日不计入工作时间
4. **非工作时间**：工作时间外的请假不计入时长

## 🔧 技术实现

### 配置参数
```javascript
workTimeConfig: {
  startHour: 9,        // 工作开始时间：9:00
  endHour: 17,         // 工作结束时间：17:00
  workHoursPerDay: 8,  // 每天工作小时数
  workDays: [1, 2, 3, 4, 5], // 工作日：周一到周五
  lunchBreak: {
    enabled: false,    // 是否启用午休时间扣除
    startHour: 12,     // 午休开始时间
    endHour: 13,       // 午休结束时间
    duration: 1        // 午休时长（小时）
  }
}
```

### 核心算法

#### 1. 同一天计算
```javascript
calculateSameDayWorkHours(start, end, workConfig) {
  // 1. 检查是否为工作日
  // 2. 获取当天工作时间范围
  // 3. 计算请假时间与工作时间的交集
  // 4. 返回交集时长（小时）
}
```

#### 2. 跨天计算
```javascript
calculateMultiDayWorkHours(start, end, workConfig) {
  // 1. 计算开始日剩余工作时间
  // 2. 计算中间完整工作日时间
  // 3. 计算结束日工作时间
  // 4. 累加总时长
}
```

## 📊 计算示例

### 示例1：同一天请假
- **请假时间**：2025-06-06 14:00 至 2025-06-06 16:00
- **工作时间**：9:00-17:00
- **计算结果**：2小时

### 示例2：跨天请假（您的例子）
- **请假时间**：2025-06-06 16:17 至 2025-06-09 16:17
- **计算过程**：
  - 6月6日：16:17-17:00 = 43分钟
  - 6月7日：周六，非工作日 = 0小时
  - 6月8日：周日，非工作日 = 0小时
  - 6月9日：9:00-16:17 = 7小时17分钟
- **总计**：8小时（43分钟 + 7小时17分钟）

### 示例3：完整工作日请假
- **请假时间**：2025-06-06 09:00 至 2025-06-10 17:00
- **计算过程**：
  - 6月6日：9:00-17:00 = 8小时
  - 6月7日：周六 = 0小时
  - 6月8日：周日 = 0小时
  - 6月9日：9:00-17:00 = 8小时
  - 6月10日：9:00-17:00 = 8小时
- **总计**：3天（24小时）

## 🎯 显示格式

### 时长格式化
- **小于1小时**：显示分钟（如：30分钟）
- **小于8小时**：显示小时+分钟（如：3小时30分钟）
- **8小时及以上**：显示天+小时+分钟（如：2天3小时30分钟）

### 详细信息显示
- 工作时间范围
- 计算说明
- 工作制度说明

## 🔍 边界情况处理

### 1. 时间验证
- 检查时间格式有效性
- 验证结束时间晚于开始时间
- 处理时区问题

### 2. 非工作时间
- 完全在非工作时间的请假显示为0
- 跨越非工作时间的请假只计算工作时间部分

### 3. 特殊情况
- 节假日处理（可扩展）
- 调休日处理（可扩展）
- 加班时间处理（可扩展）

## 🛠️ 代码结构

### 主要方法
1. `calculateDuration(startTime, endTime)` - 主计算方法
2. `calculateSameDayWorkHours(start, end, config)` - 同一天计算
3. `calculateMultiDayWorkHours(start, end, config)` - 跨天计算

### 配置管理
- 工作时间可配置
- 工作日可配置
- 午休时间可配置（预留）

### 错误处理
- 时间格式错误
- 时间范围错误
- 计算异常处理

## 🚀 扩展功能

### 可扩展特性
1. **午休时间扣除**：可配置是否扣除午休时间
2. **节假日管理**：集成节假日API
3. **弹性工作制**：支持不同部门的工作时间
4. **加班补偿**：加班时间可抵扣请假时间

### 配置示例
```javascript
// 启用午休扣除
lunchBreak: {
  enabled: true,
  startHour: 12,
  endHour: 13,
  duration: 1
}

// 弹性工作制
flexWorkTime: {
  enabled: true,
  coreHours: { start: 10, end: 15 }, // 核心工作时间
  totalHours: 8 // 每天总工作时间
}
```

## 📈 性能优化

### 计算优化
- 避免重复计算
- 缓存工作日判断
- 优化日期操作

### 用户体验
- 实时计算显示
- 友好的错误提示
- 详细的计算说明

## 🎉 总结

这个请假时长计算系统：
- **精确计算**：按8小时工作制精确计算
- **灵活配置**：支持不同工作时间配置
- **用户友好**：清晰的显示和说明
- **易于扩展**：预留多种扩展接口

通过这个系统，用户可以准确了解自己的请假时长，HR也能精确统计员工的请假情况，确保考勤管理的准确性和公平性。
