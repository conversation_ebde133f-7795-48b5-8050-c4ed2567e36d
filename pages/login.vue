<template>
	<view class="normal-login-container">
		<view class="logo-content align-center justify-center flex">
<!--			<image style="width: 100rpx;height: 100rpx;" :src="globalConfig.appInfo.logo" mode="widthFix">-->
<!--			</image>-->
			<text class="title">中山市统一运维平台移动端</text>
		</view>
		<view class="login-form-content">
			<view class="input-item flex align-center" v-if="tenantEnabled">
				<view class="iconfont icon-share icon"></view>
				<picker @change="bindPickerChange" :value="loginForm.tenantId" :range="tenantList" range-key="name">
					<view class="input">
						{{ tenantList[selectedTenantIndex] ? tenantList[selectedTenantIndex].name : '请选择租户' }}
					</view>
				</picker>
			</view>
			<view class="input-item flex align-center">
				<view class="iconfont icon-user icon"></view>
				<input v-model="loginForm.username" class="input" type="text" placeholder="请输入账号" maxlength="30" />
			</view>
			<view class="input-item flex align-center">
				<view class="iconfont icon-password icon"></view>
				<input v-model="loginForm.password" type="password" class="input" placeholder="请输入密码" maxlength="20" />
			</view>
			<view class="input-item flex align-center" style="width: 60%;margin: 0px;" v-if="captchaEnabled">
				<view class="iconfont icon-code icon"></view>
				<input v-model="loginForm.code" type="number" class="input" placeholder="请输入验证码" maxlength="4" />
				<view class="login-code">
					<image :src="codeUrl" @click="getCode" class="login-code-img"></image>
				</view>
			</view>
			<view class="action-btn">
				<button @click="handleLogin" class="login-btn cu-btn block bg-blue lg round" :disabled="isLoading">
					<text v-if="!isLoading">登录</text>
					<text v-else>加载中...</text>
				</button>
			</view>
<!--			<view class="reg text-center" v-if="register">-->
<!--				<text class="text-grey1">没有账号？</text>-->
<!--				<text @click="handleUserRegister" class="text-blue">立即注册</text>-->
<!--			</view>-->
			<view class="xieyi text-center">
<!--				<text class="text-grey1">登录即代表同意</text>-->
<!--				<text @click="handleUserAgrement" class="text-blue">《用户协议》</text>-->
<!--				<text @click="handlePrivacy" class="text-blue">《隐私协议》</text>-->
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getCodeImg,
		getTenantList
	} from '@/api/login'

  import config from '@/config'

	export default {
		data() {
			return {
				codeUrl: "",
				captchaEnabled: true,
				tenantEnabled: true,
				// 用户注册开关
				register: false,
				globalConfig: getApp().globalData.config,
				loginForm: {
					username: "",
					// username: "admin",
					password: "",
					// password: "JX_jx#888888",
					code: "",
					uuid: '',
					tenantId: null, // 初始化为 null,
					grantType:'',
					clientId:''
				},
				tenantList: [], // 存储租户列表
				selectedTenantIndex: 0, // 默认选中第一个租户的索引
				isLoading: false, // 添加加载状态
			}
		},
		created() {
			this.getCode()
			this.getTenant()
		},
		methods: {
			// 用户注册
			handleUserRegister() {
				this.$tab.redirectTo(`/pages/register`)
			},
			// 隐私协议
			handlePrivacy() {
				let site = this.globalConfig.appInfo.agreements[0]
				this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
			},
			// 用户协议
			handleUserAgrement() {
				let site = this.globalConfig.appInfo.agreements[1]
				this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
			},
			// 获取图形验证码
			getCode() {
				getCodeImg().then(res => {
					this.captchaEnabled = res.data.captchaEnabled === undefined ? true : res.data.captchaEnabled
					if (this.captchaEnabled) {
						this.codeUrl = 'data:image/gif;base64,' + res.data.img
						this.loginForm.uuid = res.data.uuid
					}
				})
			},
			// 获取租户列表
			getTenant() {
				getTenantList().then(res => {
					this.tenantEnabled = res.tenantEnabled === undefined ? true : res.data.tenantEnabled
					if (this.tenantEnabled) {
						this.tenantList = res.data.voList.map((tenant, index) => ({
							id: tenant.tenantId,
							name: tenant.companyName
						}));
						if (this.tenantList.length > 0) {
							this.selectedTenantIndex = 0; // 默认选中第一个租户的索引
							this.loginForm.tenantId = this.tenantList[0].id; // 默认选中第一个租户的 id
						}
					}

				})
			},
			// 登录方法
			async handleLogin() {
				if (this.loginForm.username === "") {
					this.$modal.msgError("请输入您的账号")
				} else if (this.loginForm.password === "") {
					this.$modal.msgError("请输入您的密码")
				} else if (this.loginForm.code === "" && this.captchaEnabled) {
					this.$modal.msgError("请输入验证码")
				} else {
					this.isLoading = true; // 设置加载状态为 true
					this.$modal.loading("登录中，请耐心等待...")
					await this.pwdLogin();
					// 在这里不再设置 isLoading = false，而是在 pwdLogin 中处理
				}
			},
			// 登录成功后，处理函数
			async loginSuccess() {
				// 设置用户信息
				await this.$store.dispatch('GetInfo'); // 确保获取用户信息完成
				this.$modal.closeLoading(); // 关闭加载状态
				this.$tab.reLaunch('/pages/index'); // 立即跳转到首页
			},
			// 密码登录
			async pwdLogin() {
				const loginFormData = {};
				for (let key in this.loginForm) {
					if (this.loginForm[key] !== '') {
						loginFormData[key] = this.loginForm[key];
					}
				}
				loginFormData.clientId = config.itsmClientId;
				loginFormData.grantType = config.grantType;
				loginFormData.rememberMe = config.rememberMe;

				try {
					await this.$store.dispatch('Login', loginFormData);
					await this.loginSuccess(); // 确保在登录成功后立即跳转

          this.isLoading = false; // 请求完成后设置加载状态为 false
          this.$modal.closeLoading(); // 立即关闭加载提示
				} catch (error) {
					this.$modal.closeLoading();
					this.isLoading = false; // 请求完成后设置加载状态为 false
					this.$modal.msgError("登录失败，请检查您的账号和密码"); // 提示用户登录失败
					if (this.captchaEnabled) {
						this.getCode(); // 如果启用了验证码，重新获取验证码
					}
				}
			},
			// 下拉选择框值变化事件
			bindPickerChange(e) {
				this.selectedTenantIndex = e.detail.value;
				this.loginForm.tenantId = this.tenantList[this.selectedTenantIndex].id;
			},
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #ffffff;
	}

	.normal-login-container {
		width: 100%;

		.logo-content {
			width: 100%;
			font-size: 21px;
			text-align: center;
			padding-top: 15%;

			image {
				border-radius: 4px;
			}

			.title {
				margin-left: 10px;
			}
		}

		.login-form-content {
			text-align: center;
			margin: 20px auto;
			margin-top: 15%;
			width: 80%;

			.input-item {
				margin: 20px auto;
				background-color: #f5f6f7;
				height: 45px;
				border-radius: 20px;

				.icon {
					font-size: 38rpx;
					margin-left: 10px;
					color: #999;
				}

				.input {
					width: 100%;
					font-size: 14px;
					line-height: 20px;
					text-align: left;
					padding-left: 15px;
				}

			}

			.login-btn {
				margin-top: 40px;
				height: 45px;
			}

			.reg {
				margin-top: 15px;
			}

			.xieyi {
				color: #333;
				margin-top: 20px;
			}

			.login-code {
				height: 38px;
				float: right;

				.login-code-img {
					height: 38px;
					position: absolute;
					margin-left: 10px;
					width: 200rpx;
				}
			}
		}
	}
</style>
