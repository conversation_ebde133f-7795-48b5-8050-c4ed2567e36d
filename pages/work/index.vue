<template>
  <view class="work-container">
    <!-- 顶部欢迎区域 -->
    <view class="welcome-section">
      <view class="welcome-content">
        <view class="greeting">
          <text class="greeting-text">{{ greetingText }}</text>
          <text class="user-name">{{ userName }}</text>
        </view>
        <view class="date-info">
          <text class="current-date">{{ currentDate }}</text>
          <text class="weather-info">{{ weatherInfo }}</text>
        </view>
      </view>
      <view class="welcome-decoration">
        <view class="decoration-circle circle-1"></view>
        <view class="decoration-circle circle-2"></view>
        <view class="decoration-circle circle-3"></view>
      </view>
    </view>

    <!-- 快捷操作区域 -->
    <view class="quick-actions">
      <view class="section-header">
        <view class="section-title">
          <view class="title-icon">⚡</view>
          <text class="title-text">快捷操作</text>
        </view>
        <view class="section-subtitle">
          <text>常用功能一键直达</text>
        </view>
      </view>

      <view class="actions-grid">
        <view class="action-card primary" @click="handleToLeave">
          <view class="card-background">
            <view class="bg-pattern pattern-1"></view>
            <view class="bg-pattern pattern-2"></view>
          </view>
          <view class="card-content">
            <view class="icon-wrapper primary-icon">
              <text class="action-icon">🏖️</text>
            </view>
            <view class="action-info">
              <text class="action-title">请假申请</text>
              <text class="action-desc">休假申请管理</text>
            </view>
            <view class="action-arrow">→</view>
          </view>
        </view>

        <view class="action-card secondary" @click="handleWorkOvertimeClick('加班')">
          <view class="card-background">
            <view class="bg-pattern pattern-3"></view>
            <view class="bg-pattern pattern-4"></view>
          </view>
          <view class="card-content">
            <view class="icon-wrapper secondary-icon">
              <text class="action-icon">💼</text>
            </view>
            <view class="action-info">
              <text class="action-title">加班申请</text>
              <text class="action-desc">加班时间登记</text>
            </view>
            <view class="action-arrow">→</view>
          </view>
        </view>

        <view class="action-card tertiary" @click="handleGoOutClick('外出')">
          <view class="card-background">
            <view class="bg-pattern pattern-5"></view>
            <view class="bg-pattern pattern-6"></view>
          </view>
          <view class="card-content">
            <view class="icon-wrapper tertiary-icon">
              <text class="action-icon">🚗</text>
            </view>
            <view class="action-info">
              <text class="action-title">外出申请</text>
              <text class="action-desc">外出事务登记</text>
            </view>
            <view class="action-arrow">→</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 统计概览区域 -->
    <view class="stats-section">
      <view class="section-header">
        <view class="section-title">
          <view class="title-icon">📊</view>
          <text class="title-text">本月概览</text>
        </view>
      </view>

      <view class="stats-grid">
        <view class="stat-item">
          <view class="stat-number">{{ monthlyStats.leave }}</view>
          <view class="stat-label">请假天数</view>
          <view class="stat-trend up">↗ +2</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{ monthlyStats.overtime }}</view>
          <view class="stat-label">加班小时</view>
          <view class="stat-trend down">↘ -5</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{ monthlyStats.goout }}</view>
          <view class="stat-label">外出次数</view>
          <view class="stat-trend stable">→ 0</view>
        </view>
      </view>
    </view>

    <!-- 最近动态区域 -->
    <view class="recent-activities">
      <view class="section-header">
        <view class="section-title">
          <view class="title-icon">🕒</view>
          <text class="title-text">最近动态</text>
        </view>
        <view class="view-all" @click="viewAllActivities">
          <text>查看全部</text>
          <text class="arrow">→</text>
        </view>
      </view>

      <view class="activity-list">
        <view class="activity-item" v-for="(activity, index) in recentActivities" :key="index">
          <view class="activity-dot" :class="activity.type"></view>
          <view class="activity-content">
            <view class="activity-title">{{ activity.title }}</view>
            <view class="activity-time">{{ activity.time }}</view>
          </view>
          <view class="activity-status" :class="activity.status">
            {{ activity.statusText }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userName: '张三', // 可以从用户信息中获取
      currentDate: '',
      weatherInfo: '晴朗 22°C',
      greetingText: '',
      monthlyStats: {
        leave: 2,
        overtime: 18,
        goout: 3
      },
      recentActivities: [
        {
          title: '请假申请已通过',
          time: '2小时前',
          type: 'success',
          status: 'approved',
          statusText: '已通过'
        },
        {
          title: '加班申请待审核',
          time: '1天前',
          type: 'pending',
          status: 'pending',
          statusText: '待审核'
        },
        {
          title: '外出申请已提交',
          time: '2天前',
          type: 'info',
          status: 'submitted',
          statusText: '已提交'
        }
      ]
    }
  },

  mounted() {
    this.initPageData()
  },

  methods: {
    // 初始化页面数据
    initPageData() {
      this.updateDateTime()
      this.updateGreeting()
      // 每分钟更新一次时间
      setInterval(() => {
        this.updateDateTime()
        this.updateGreeting()
      }, 60000)
    },

    // 更新日期时间
    updateDateTime() {
      const now = new Date()
      const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      }
      this.currentDate = now.toLocaleDateString('zh-CN', options)
    },

    // 更新问候语
    updateGreeting() {
      const hour = new Date().getHours()
      if (hour < 6) {
        this.greetingText = '夜深了，'
      } else if (hour < 12) {
        this.greetingText = '早上好，'
      } else if (hour < 18) {
        this.greetingText = '下午好，'
      } else {
        this.greetingText = '晚上好，'
      }
    },

    // 请假申请
    handleToLeave() {
      uni.showLoading({ title: '加载中...' })
      setTimeout(() => {
        uni.hideLoading()
        this.$tab.navigateTo(`/pages/mine/leave/index`)
      }, 300)
    },

    // 加班申请
    handleWorkOvertimeClick(message) {
      if (message === '加班') {
        uni.showLoading({ title: '加载中...' })
        setTimeout(() => {
          uni.hideLoading()
          this.navTo('/pages/mine/workOvertime/index')
        }, 300)
      }
    },

    // 外出申请
    handleGoOutClick(message) {
      if (message === '外出') {
        uni.showLoading({ title: '加载中...' })
        setTimeout(() => {
          uni.hideLoading()
          this.navTo('/pages/mine/goOut/index')
        }, 300)
      }
    },

    // 查看全部动态
    viewAllActivities() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    // 页面导航
    navTo(url) {
      uni.navigateTo({
        url: url
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/* 页面基础样式 */
page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.work-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0 24rpx 120rpx;
  position: relative;
  overflow: hidden;
}

/* 欢迎区域样式 */
.welcome-section {
  position: relative;
  padding: 60rpx 32rpx 40rpx;
  margin: 0 -24rpx 40rpx;
  background: linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.05) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 0 0 40rpx 40rpx;
  overflow: hidden;

  .welcome-content {
    position: relative;
    z-index: 2;

    .greeting {
      margin-bottom: 16rpx;

      .greeting-text {
        font-size: 32rpx;
        color: rgba(255,255,255,0.9);
        font-weight: 300;
      }

      .user-name {
        font-size: 36rpx;
        color: #fff;
        font-weight: 600;
        margin-left: 8rpx;
      }
    }

    .date-info {
      display: flex;
      align-items: center;
      gap: 24rpx;

      .current-date {
        font-size: 28rpx;
        color: rgba(255,255,255,0.8);
      }

      .weather-info {
        font-size: 24rpx;
        color: rgba(255,255,255,0.7);
        background: rgba(255,255,255,0.1);
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        backdrop-filter: blur(10rpx);
      }
    }
  }

  .welcome-decoration {
    position: absolute;
    top: 0;
    right: 0;
    width: 200rpx;
    height: 200rpx;

    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255,255,255,0.1);

      &.circle-1 {
        width: 120rpx;
        height: 120rpx;
        top: -20rpx;
        right: -20rpx;
        animation: float 6s ease-in-out infinite;
      }

      &.circle-2 {
        width: 80rpx;
        height: 80rpx;
        top: 60rpx;
        right: 40rpx;
        animation: float 4s ease-in-out infinite reverse;
      }

      &.circle-3 {
        width: 40rpx;
        height: 40rpx;
        top: 20rpx;
        right: 120rpx;
        animation: float 5s ease-in-out infinite;
      }
    }
  }
}

/* 快捷操作区域 */
.quick-actions {
  margin-bottom: 40rpx;

  .section-header {
    margin-bottom: 32rpx;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 8rpx;

      .title-icon {
        font-size: 32rpx;
        margin-right: 12rpx;
      }

      .title-text {
        font-size: 36rpx;
        font-weight: 600;
        color: #fff;
      }
    }

    .section-subtitle {
      text {
        font-size: 26rpx;
        color: rgba(255,255,255,0.7);
      }
    }
  }

  .actions-grid {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
  }

  .action-card {
    position: relative;
    background: #fff;
    border-radius: 24rpx;
    padding: 32rpx;
    box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:active {
      transform: scale(0.98);
      box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.15);
    }

    .card-background {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      overflow: hidden;

      .bg-pattern {
        position: absolute;
        border-radius: 50%;
        opacity: 0.1;

        &.pattern-1 {
          width: 120rpx;
          height: 120rpx;
          top: -40rpx;
          right: -40rpx;
          background: linear-gradient(45deg, #667eea, #764ba2);
        }

        &.pattern-2 {
          width: 80rpx;
          height: 80rpx;
          bottom: -20rpx;
          left: -20rpx;
          background: linear-gradient(45deg, #764ba2, #667eea);
        }

        &.pattern-3 {
          width: 100rpx;
          height: 100rpx;
          top: -30rpx;
          right: -30rpx;
          background: linear-gradient(45deg, #f093fb, #f5576c);
        }

        &.pattern-4 {
          width: 60rpx;
          height: 60rpx;
          bottom: -10rpx;
          left: -10rpx;
          background: linear-gradient(45deg, #f5576c, #f093fb);
        }

        &.pattern-5 {
          width: 110rpx;
          height: 110rpx;
          top: -35rpx;
          right: -35rpx;
          background: linear-gradient(45deg, #4facfe, #00f2fe);
        }

        &.pattern-6 {
          width: 70rpx;
          height: 70rpx;
          bottom: -15rpx;
          left: -15rpx;
          background: linear-gradient(45deg, #00f2fe, #4facfe);
        }
      }
    }

    .card-content {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;

      .icon-wrapper {
        width: 80rpx;
        height: 80rpx;
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;

        &.primary-icon {
          background: linear-gradient(135deg, #667eea, #764ba2);
        }

        &.secondary-icon {
          background: linear-gradient(135deg, #f093fb, #f5576c);
        }

        &.tertiary-icon {
          background: linear-gradient(135deg, #4facfe, #00f2fe);
        }

        .action-icon {
          font-size: 40rpx;
        }
      }

      .action-info {
        flex: 1;

        .action-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 8rpx;
          display: block;
        }

        .action-desc {
          font-size: 26rpx;
          color: #666;
          display: block;
        }
      }

      .action-arrow {
        font-size: 32rpx;
        color: #999;
        font-weight: 300;
      }
    }
  }
}

/* 统计概览区域 */
.stats-section {
  margin-bottom: 40rpx;

  .section-header {
    margin-bottom: 32rpx;

    .section-title {
      display: flex;
      align-items: center;

      .title-icon {
        font-size: 32rpx;
        margin-right: 12rpx;
      }

      .title-text {
        font-size: 36rpx;
        font-weight: 600;
        color: #fff;
      }
    }
  }

  .stats-grid {
    display: flex;
    gap: 20rpx;

    .stat-item {
      flex: 1;
      background: rgba(255,255,255,0.15);
      backdrop-filter: blur(20rpx);
      border-radius: 20rpx;
      padding: 32rpx 24rpx;
      text-align: center;
      border: 1px solid rgba(255,255,255,0.2);

      .stat-number {
        font-size: 48rpx;
        font-weight: 700;
        color: #fff;
        margin-bottom: 8rpx;
        display: block;
      }

      .stat-label {
        font-size: 24rpx;
        color: rgba(255,255,255,0.8);
        margin-bottom: 12rpx;
        display: block;
      }

      .stat-trend {
        font-size: 22rpx;
        padding: 4rpx 12rpx;
        border-radius: 12rpx;
        display: inline-block;

        &.up {
          background: rgba(76, 175, 80, 0.2);
          color: #4CAF50;
        }

        &.down {
          background: rgba(244, 67, 54, 0.2);
          color: #F44336;
        }

        &.stable {
          background: rgba(255, 193, 7, 0.2);
          color: #FFC107;
        }
      }
    }
  }
}

/* 最近动态区域 */
.recent-activities {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32rpx;

    .section-title {
      display: flex;
      align-items: center;

      .title-icon {
        font-size: 32rpx;
        margin-right: 12rpx;
      }

      .title-text {
        font-size: 36rpx;
        font-weight: 600;
        color: #fff;
      }
    }

    .view-all {
      display: flex;
      align-items: center;
      gap: 8rpx;

      text {
        font-size: 26rpx;
        color: rgba(255,255,255,0.8);
      }

      .arrow {
        font-size: 24rpx;
        color: rgba(255,255,255,0.6);
      }
    }
  }

  .activity-list {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(20rpx);
    border-radius: 20rpx;
    padding: 24rpx;
    border: 1px solid rgba(255,255,255,0.15);

    .activity-item {
      display: flex;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1px solid rgba(255,255,255,0.1);

      &:last-child {
        border-bottom: none;
      }

      .activity-dot {
        width: 16rpx;
        height: 16rpx;
        border-radius: 50%;
        margin-right: 24rpx;
        flex-shrink: 0;

        &.success {
          background: #4CAF50;
          box-shadow: 0 0 0 4rpx rgba(76, 175, 80, 0.2);
        }

        &.pending {
          background: #FF9800;
          box-shadow: 0 0 0 4rpx rgba(255, 152, 0, 0.2);
        }

        &.info {
          background: #2196F3;
          box-shadow: 0 0 0 4rpx rgba(33, 150, 243, 0.2);
        }
      }

      .activity-content {
        flex: 1;

        .activity-title {
          font-size: 28rpx;
          color: #fff;
          margin-bottom: 8rpx;
          display: block;
        }

        .activity-time {
          font-size: 24rpx;
          color: rgba(255,255,255,0.6);
          display: block;
        }
      }

      .activity-status {
        font-size: 24rpx;
        padding: 8rpx 16rpx;
        border-radius: 12rpx;

        &.approved {
          background: rgba(76, 175, 80, 0.2);
          color: #4CAF50;
        }

        &.pending {
          background: rgba(255, 152, 0, 0.2);
          color: #FF9800;
        }

        &.submitted {
          background: rgba(33, 150, 243, 0.2);
          color: #2196F3;
        }
      }
    }
  }
}

/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 响应式设计 */
@media screen and (min-width: 500px) {
  .work-container {
    max-width: 500px;
    margin: 0 auto;
  }

  .actions-grid {
    flex-direction: row;
    flex-wrap: wrap;

    .action-card {
      width: calc(50% - 12rpx);
    }
  }
}
</style>
