<template>
  <view class="viewport dashboad-index">
    <view class="top-box">
      <!-- 头部导航栏 -->
      <view class="header-nav">
        <view class="logo">
          <image class="image" mode="heightFix" src="@/static/images/logo.png" />
        </view>
        <!-- 右上角菜单按钮 -->
        <view class="menu-button" @click="toggleMenu">
          <view class="menu-icon">
            <view class="line"></view>
            <view class="line"></view>
            <view class="line"></view>
          </view>
        </view>
      </view>

      <!-- 项目选择器 -->
      <picker :range="projects" range-key="name">
        <view class="project-selector">
            <view class="selected-project">
              <image class="icon" mode="aspectFit" src="@/static/images/icons/floder.png" />
              <text class="text">
                请选择项目
              </text>
            </view>
            <image class="icon" mode="aspectFit" src="@/static/images/icons/list_tigger.png" />
        </view>
      </picker>
    </view>

    <!-- 侧边菜单 -->
    <view v-if="showMenu" class="menu-overlay" @click="closeMenu">
      <view class="menu-container" @click.stop>
        <view class="menu-header">
          <text class="menu-title">菜单</text>
          <view class="close-button" @click="closeMenu">
            <text>✕</text>
          </view>
        </view>
        <view class="menu-list">
          <view
            v-for="(item, index) in menuItems"
            :key="index"
            class="menu-item"
            @click="navigateToPage(item)"
          >
            <image class="menu-icon" mode="aspectFit" :src="item.icon" />
            <text class="menu-text">{{ item.name }}</text>
            <text class="menu-arrow">></text>
          </view>
        </view>
      </view>
    </view>

    <view class="box person">
      <view class="title">
        <view class="content">
          <image class="icon" mode="aspectFit" src="@/static/images/icons/person.png" />
          <text class="text">人员情况</text>
        </view>
        <view class="extra">
          <text class="date">{{ currentDate }}</text>
        </view>
      </view>
      <view class="body">
        <uni-row :gutter="12" class="card-row">
          <uni-col :span="9">
            <view class="item primary">
              <view class="content">
                <view class="num">208</view>
                <view class="label">人员总数</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="5">
            <view class="item blue">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/user_group.png" />
              <view class="content">
                <view class="num">186</view>
                <view class="label">在岗</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="5">
            <view class="item green">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/calendar.png" />
              <view class="content">
                <view class="num">24</view>
                <view class="label">在岗</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="5">
            <view class="item pink">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/warning.png" />
              <view class="content">
                <view class="num">6</view>
                <view class="label">在岗</view>
              </view>
            </view>
          </uni-col>
        </uni-row>
      </view>
    </view>
    
    <view class="box ticket">
      <view class="title">
        <view class="content">
          <image class="icon" mode="aspectFit" src="@/static/images/icons/file.png" />
          <text class="text">工单情况</text>
        </view>
        <view class="extra">
          <text class="date">{{ currentDate }}</text>
        </view>
      </view>
      <view class="body">
        <uni-row :gutter="12" class="card-row">
          <uni-col :span="10">
            <view class="item primary">
              <view class="content">
                <view class="num">1051</view>
                <view class="label">当月工单数</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="7">
            <view class="item green">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/todo_checked.png" />
              <view class="content">
                <view class="num">24</view>
                <view class="label">办结</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="7">
            <view class="item blue">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/clock.png" />
              <view class="content">
                <view class="num">1018</view>
                <view class="label">进行中</view>
              </view>
            </view>
          </uni-col>
        </uni-row>

        <view class="sub-title">
          <view class="content">
            <text>工单趋势</text>
          </view>
          <view class="extra">
            <view class="tab-list">
              <view class="item active">
                <text>本月</text>
              </view>
              <view class="item">
                <text>本年</text>
              </view>
            </view>
          </view>
        </view>
        <view class="chart-wrapper">
          <ve-line
            height="230px"
            :data="lineData"
            :grid="chartGrid"
            :settings="lineChartSettings"
            :legend-visible="false"
            :extend="lineChartExtra"
            :colors="['#006ED9']"
          />
        </view>
        
        <view class="sub-title">
          <view class="content">
            <text>工单分类</text>
          </view>
          <view class="extra">
            <view class="tab-list">
              <view class="item active">
                <text>本月</text>
              </view>
              <view class="item">
                <text>本年</text>
              </view>
            </view>
          </view>
        </view>
        <view class="chart-wrapper">
          <ve-ring
            height="230px"
            :data="ringData"
            :grid="chartGrid"
            :settings="ringChartSettings"
            :extend="ringChartExtra"
          />
        </view>

      </view>
    </view>

    <view class="box system">
      <view class="title">
        <view class="content">
          <image class="icon" mode="aspectFit" src="@/static/images/icons/system.png" />
          <text class="text">系统情况</text>
        </view>
        <view class="extra">
          <text class="date">{{ currentDate }}</text>
        </view>
      </view>
      <view class="body">
        <uni-row :gutter="12" class="card-row">
          <uni-col :span="10">
            <view class="item primary">
              <view class="content">
                <view class="num">45</view>
                <view class="label">系统数量</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="7">
            <view class="item green">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/shield.png" />
              <view class="content">
                <view class="num">45</view>
                <view class="label">正常</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="7">
            <view class="item pink">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/warning.png" />
              <view class="content">
                <view class="num">0</view>
                <view class="label">异常</view>
              </view>
            </view>
          </uni-col>
        </uni-row>

        <view class="sub-title">
          <view class="content">
            <text>异常趋势</text>
          </view>
          <view class="extra">
            <view class="tab-list">
              <view class="item active">
                <text>本月</text>
              </view>
              <view class="item">
                <text>本年</text>
              </view>
            </view>
          </view>
        </view>
        <view class="chart-wrapper">
          <ve-line
            height="230px"
            :data="lineData"
            :grid="chartGrid"
            :settings="lineChartSettings"
            :legend-visible="false"
            :extend="lineChartExtra"
            :colors="['#006ED9']"
          />
        </view>
      </view>
    </view>

    <view class="box server">
      <view class="title">
        <view class="content">
          <image class="icon" mode="aspectFit" src="@/static/images/icons/server.png" />
          <text class="text">人员情况</text>
        </view>
        <view class="extra">
          <text class="date">{{ currentDate }}</text>
        </view>
      </view>
      <view class="body">
        <uni-row :gutter="12" class="card-row">
          <uni-col :span="9">
            <view class="item primary">
              <view class="content">
                <view class="num">1051</view>
                <view class="label">数量</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="5">
            <view class="item blue">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/shield.png" />
              <view class="content">
                <view class="num">186</view>
                <view class="label">正常</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="5">
            <view class="item pink">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/warning.png" />
              <view class="content">
                <view class="num">6</view>
                <view class="label">异常</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="5">
            <view class="item gray">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/shutdown.png" />
              <view class="content">
                <view class="num">2</view>
                <view class="label">关机</view>
              </view>
            </view>
          </uni-col>
        </uni-row>

        <view class="sub-title">
          <view class="content">
            <text>异常情况</text>
          </view>
          <view class="extra">
            <view class="tab-list">
              <view class="item active">
                <text>本月</text>
              </view>
              <view class="item">
                <text>本年</text>
              </view>
            </view>
          </view>
        </view>
        <view class="chart-wrapper">
          <ve-line
            height="230px"
            :data="lineData"
            :grid="chartGrid"
            :settings="lineChartSettings"
            :legend-visible="false"
            :extend="lineChartExtra"
            :colors="['#006ED9']"
          />
        </view>
      </view>
    </view>

    <view class="box assets">
      <view class="title">
        <view class="content">
          <image class="icon" mode="aspectFit" src="@/static/images/icons/assets.png" />
          <text class="text">资金情况</text>
        </view>
        <view class="extra">
          <text class="date">{{ currentDate }}</text>
        </view>
      </view>
      <view class="body">
        <uni-row :gutter="12" class="card-row">
          <uni-col :span="9">
            <view class="item primary">
              <view class="content">
                <view class="num">1051</view>
                <view class="label">数量</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="5">
            <view class="item blue">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/shield.png" />
              <view class="content">
                <view class="num">186</view>
                <view class="label">正常</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="5">
            <view class="item pink">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/warning.png" />
              <view class="content">
                <view class="num">6</view>
                <view class="label">异常</view>
              </view>
            </view>
          </uni-col>
          <uni-col :span="5">
            <view class="item gray">
              <image class="bg-icon" mode="aspectFit" src="@/static/images/icons/shutdown.png" />
              <view class="content">
                <view class="num">2</view>
                <view class="label">关机</view>
              </view>
            </view>
          </uni-col>
        </uni-row>
      </view> 
    </view>

  </view>
</template>

<script>
  // echarts 图表组件使用文档参考：https://denaro-org.github.io/v-charts2/charts/line.html
  import Vue from 'vue'
  import VeLine from '@v-charts2/line'
  import VeRing from '@v-charts2/ring'
  Vue.use(VeLine)
  Vue.use(VeRing)

  export default {
    name: 'DashboadIndex',
    components: {
      // VeLine
    },
    data() {
      return {
        // 当前日期，格式：YYYY-MM-DD
        currentDate: '',
        // 控制菜单显示状态
        showMenu: false,
        // 菜单项配置
        menuItems: [
          {
            id: 'person',
            name: '人员情况',
            icon: '@/static/images/icons/person.png',
            path: '/pages/dashboard/person'
          },
          {
            id: 'ticket',
            name: '工单情况',
            icon: '@/static/images/icons/file.png',
            path: '/pages/dashboard/ticket'
          },
          {
            id: 'system',
            name: '系统情况',
            icon: '@/static/images/icons/system.png',
            path: '/pages/dashboard/system'
          },
          {
            id: 'server',
            name: '服务器情况',
            icon: '@/static/images/icons/server.png',
            path: '/pages/dashboard/server'
          },
          {
            id: 'assets',
            name: '资金情况',
            icon: '@/static/images/icons/assets.png',
            path: '/pages/dashboard/assets'
          }
        ],
        projects: [
          {
            id: 1,
            name: '项目1'
          },
          {
            id: 2,
            name: '项目2'
          },
          {
            id: 3,
            name: '项目3'
          }
        ],
        chartGrid: {
          left: 10,
          right: 10,
          top: 10,
          bottom: 10,
        },
        lineChartExtra: {
          series: {
            smooth: false,
          }
        },
        lineData: {
          columns: ['时间', '工单数量'],
          rows: [
            { '时间': '08:00', '工单数量': 100 },
            { '时间': '09:00', '工单数量': 150 },
            { '时间': '10:00', '工单数量': 200 },
            { '时间': '11:00', '工单数量': 180 },
            { '时间': '12:00', '工单数量': 80 },
            { '时间': '13:00', '工单数量': 110 },
            { '时间': '14:00', '工单数量': 250 },
            { '时间': '15:00', '工单数量': 210 },
          ],
        },
        ringData: {
          columns: ['类型', '数量'],
          rows: [
            { '类型': '类型1', '数量': 100 },
            { '类型': '类型2', '数量': 150 },
            { '类型': '类型3', '数量': 200 },
          ]
        },
        ringChartExtra: {
          legend: {
            bottom: 0,
          }
        },
        ringChartSettings: {
          radius: ['40%', '60%'],
          offsetY: 100,
        }
      }
    },

    // 生命周期方法
    mounted() {
      // 组件加载时初始化当前日期
      this.initCurrentDate()
    },

    // 组件方法
    methods: {
      /**
       * 初始化当前日期
       * 格式化为 YYYY-MM-DD 格式
       */
      initCurrentDate() {
        const now = new Date()
        const year = now.getFullYear()
        const month = String(now.getMonth() + 1).padStart(2, '0')
        const day = String(now.getDate()).padStart(2, '0')

        this.currentDate = `${year}-${month}-${day}`

        console.log('初始化当前日期:', this.currentDate)
      },

      /**
       * 切换菜单显示状态
       */
      toggleMenu() {
        this.showMenu = !this.showMenu
        console.log('切换菜单状态:', this.showMenu)
      },

      /**
       * 关闭菜单
       */
      closeMenu() {
        this.showMenu = false
        console.log('关闭菜单')
      },

      /**
       * 导航到指定页面
       * @param {Object} menuItem - 菜单项对象
       */
      navigateToPage(menuItem) {
        console.log('导航到页面:', menuItem.name, menuItem.path)

        // 关闭菜单
        this.closeMenu()

        // 导航到对应页面
        uni.navigateTo({
          url: menuItem.path,
          success: () => {
            console.log('导航成功:', menuItem.path)
          },
          fail: (err) => {
            console.error('导航失败:', err)
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            })
          }
        })
      }
    }
  }
</script>

<style lang="scss">
@import url("@/static/scss/dashboard.scss");

.dashboad-index {
  padding-bottom: 30rpx;
  background: url('@/static/images/dashboard_bg.png') no-repeat center top;
  background-size: contain;
  .top-box {
    width: 100%;
    padding: 214rpx 24rpx 34rpx 24rpx;
    .logo {
      height: 56rpx;
      margin-bottom: 30rpx;
    }
    .project-selector {
      color: #FFF;
      line-height: 80rpx;
      background: rgba(255, 255, 255, 0.2);
      padding: 0 24rpx;
      border-radius: 10rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .icon {
        width: 32rpx;
        height: 32rpx;
      }
      .text {
        margin: 0 10rpx;
      }
    }
  }
  .box {
    margin: 0 24rpx 24rpx 24rpx;
    .title,
    .sub-title {
      display: flex;
      margin-bottom: 30rpx;
      .icon {
        width: 36rpx;
        height: 36rpx;
        margin-right: 10rpx;
      }
      .content {
        display: inline-flex;
        align-items: center;
        color: #333;
        font-size: 36rpx;
        font-weight: 600;
      }
      .extra {
        display: inline-flex;
        align-items: center;
        color: #999;
        margin-left: auto;
      }
    }
    .sub-title {
      margin: 30rpx 0;
      .content {
        &::before {
          content: '';
          display: inline-block;
          width: 7rpx;
          height: 30rpx;
          border-radius: 9rpx;
          background: #006ED9;
          margin-right: 16rpx;
        }
      }
      .tab-list {
        display: inline-flex;
        align-items: center;
        .item {
          color: #BBB;
          background: #FAFAFA;
          border-radius: 4rpx;
          line-height: 42rpx;
          padding: 0 16rpx;
          margin-left: 12rpx;
          &.active {
            color: #006ED9;
            background: #F2FAFF;
          }
        }
      }
    }
    .chart-wrapper {
      height: 460rpx;
    }
  }
  .card-row {
    .item {
      display: flex;
      position: relative;
      height: 136rpx;
      align-items: flex-end;
      color: #747474;
      padding: 16rpx;
      font-size: 28rpx;
      font-weight: 400;
      border-radius: 8rpx;
      .bg-icon {
        position: absolute;
        top: 8rpx;
        right: 10rpx;
        width: 50rpx;
        height: 50rpx;
        z-index: 0;
      }
      .content {
        position: relative;
        z-index: 1;
      }
      .num {
        color: #333;
        font-weight: bold;
        font-size: 36rpx;
      }
      &.primary {
        .num {
          font-size: 48rpx;
        }
      }
      &.blue {
        background: linear-gradient( 180deg, #BCE2FF 0%, #E7F4FF 100%);
        .label {
          color: #509CE4;
        }
      }
      &.green {
        background: linear-gradient( 360deg, #EFFFFB 0%, #DEFAED 100%);
       .label {
          color: #2BA97B;
        }
      }
      &.pink {
        background: linear-gradient( 180deg, #FFEBEB 0%, #FFF8F9 100%);
        .label {
          color: #EA6055;
        }
      }
      &.gray {
        background: linear-gradient( 180deg, #E9E9E9 0%, #F3F3F3 100%);
       .label {
          color: #747474;
        }
      }
    }
  }
}
</style>