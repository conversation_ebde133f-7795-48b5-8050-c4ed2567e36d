<template>
  <!--
    自定义日期时间选择器组件
    解决原生uni-datetime-picker在移动端显示异常的问题

    主要特性：
    1. 移动端优化：专门为移动端设计的底部弹窗样式
    2. 兼容性强：使用uni-app原生组件，确保跨平台兼容
    3. 层级管理：使用高z-index确保弹窗正确显示
    4. 动态计算：正确处理不同月份天数，包括闰年
  -->
  <view class="datetime-picker-fix">
    <!--
      输入框显示区域
      点击后触发日期选择器弹窗
    -->
    <view class="picker-input" @click="openPicker">
      <text class="picker-text" :class="{ 'placeholder': !displayValue }">
        {{ displayValue || placeholder }}
      </text>
      <!-- 日历图标 -->
      <text class="picker-icon">📅</text>
    </view>

    <!--
      自定义弹窗遮罩层
      使用固定定位确保在所有情况下都能正确显示
      z-index设置为10000确保在最顶层
    -->
    <view v-if="showPicker" class="picker-overlay" @click="closePicker">
      <!--
        弹窗容器
        使用@click.stop阻止事件冒泡，防止点击内容区域时关闭弹窗
      -->
      <view class="picker-container" @click.stop>
        <!-- 弹窗头部：包含标题和操作按钮 -->
        <view class="picker-header">
          <text class="picker-cancel" @click="closePicker">取消</text>
          <text class="picker-title">{{ title }}</text>
          <text class="picker-confirm" @click="confirmSelection">确定</text>
        </view>

        <!--
          日期时间选择器主体
          使用picker-view组件实现滚动选择
          支持年、月、日、时、分的选择
        -->
        <picker-view
          :value="pickerValue"
          @change="onPickerViewChange"
          class="picker-view-container"
        >
          <!-- 年份选择列 -->
          <picker-view-column>
            <view
              v-for="(item, index) in pickerRange[0]"
              :key="`year-${index}`"
              class="picker-item"
            >
              {{ item }}
            </view>
          </picker-view-column>

          <!-- 月份选择列 -->
          <picker-view-column>
            <view
              v-for="(item, index) in pickerRange[1]"
              :key="`month-${index}`"
              class="picker-item"
            >
              {{ item }}
            </view>
          </picker-view-column>

          <!-- 日期选择列 -->
          <picker-view-column>
            <view
              v-for="(item, index) in pickerRange[2]"
              :key="`day-${index}`"
              class="picker-item"
            >
              {{ item }}
            </view>
          </picker-view-column>

          <!-- 小时选择列（仅在datetime模式下显示） -->
          <picker-view-column v-if="type === 'datetime'">
            <view
              v-for="(item, index) in pickerRange[3]"
              :key="`hour-${index}`"
              class="picker-item"
            >
              {{ item }}
            </view>
          </picker-view-column>

          <!-- 分钟选择列（仅在datetime模式下显示） -->
          <picker-view-column v-if="type === 'datetime'">
            <view
              v-for="(item, index) in pickerRange[4]"
              :key="`minute-${index}`"
              class="picker-item"
            >
              {{ item }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
  </view>
</template>

<script>
/**
 * 自定义日期时间选择器组件
 *
 * 解决问题：
 * 1. 原生uni-datetime-picker在移动端显示异常
 * 2. 日历弹窗层级冲突导致显示不正确
 * 3. 移动端适配问题
 *
 * 技术方案：
 * 1. 使用picker-view组件替代复杂的日历组件
 * 2. 采用底部弹窗设计，符合移动端用户习惯
 * 3. 使用固定定位和高z-index确保正确显示
 * 4. 动态计算月份天数，正确处理闰年
 */
export default {
  name: 'DatetimePickerFix',

  // 组件属性定义
  props: {
    // v-model绑定的值，格式：YYYY-MM-DD HH:mm:ss 或 YYYY-MM-DD
    value: {
      type: String,
      default: ''
    },
    // 选择器类型：'date' 仅日期，'datetime' 日期+时间
    type: {
      type: String,
      default: 'datetime',
      validator: (value) => ['date', 'datetime'].includes(value)
    },
    // 占位符文本
    placeholder: {
      type: String,
      default: '请选择日期时间'
    },
    // 弹窗标题
    title: {
      type: String,
      default: '选择日期时间'
    }
  },

  // 组件数据
  data() {
    return {
      // 控制弹窗显示状态
      showPicker: false,
      // picker-view的选中值数组 [年份索引, 月份索引, 日期索引, 小时索引, 分钟索引]
      pickerValue: [0, 0, 0, 0, 0],
      // picker-view的选项数组 [年份数组, 月份数组, 日期数组, 小时数组, 分钟数组]
      pickerRange: [[], [], [], [], []]
    }
  },
  // 计算属性
  computed: {
    /**
     * 显示在输入框中的格式化日期时间字符串
     * 根据type属性决定显示格式：
     * - datetime: YYYY-MM-DD HH:mm
     * - date: YYYY-MM-DD
     */
    displayValue() {
      // 如果没有值，返回空字符串显示占位符
      if (!this.value) return ''

      try {
        // 创建Date对象并验证有效性
        const date = new Date(this.value)
        if (isNaN(date.getTime())) return ''

        // 格式化年月日
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')

        // 根据类型返回不同格式
        if (this.type === 'datetime') {
          const hour = String(date.getHours()).padStart(2, '0')
          const minute = String(date.getMinutes()).padStart(2, '0')
          return `${year}-${month}-${day} ${hour}:${minute}`
        } else {
          return `${year}-${month}-${day}`
        }
      } catch (e) {
        // 日期解析失败时返回空字符串
        console.warn('日期格式解析失败:', this.value, e)
        return ''
      }
    }
  },
  // 监听器
  watch: {
    /**
     * 监听value变化，当外部传入新值时重新初始化选择器
     * immediate: true 确保组件初始化时也会执行
     */
    value: {
      handler(newVal, oldVal) {
        // 只有当值真正改变时才重新初始化，避免无限循环
        if (newVal !== oldVal) {
          this.initPickerValue()
        }
      },
      immediate: true
    }
  },

  // 生命周期钩子
  mounted() {
    // 初始化选择器的选项范围（年月日时分的可选值）
    this.initPickerRange()
    // 初始化选择器的当前选中值
    this.initPickerValue()
  },
  // 组件方法
  methods: {
    /**
     * 初始化选择器的选项范围
     * 生成年、月、日、时、分的所有可选项
     */
    initPickerRange() {
      // 生成年份范围（当前年份前后10年，共21年）
      const currentYear = new Date().getFullYear()
      const years = []
      for (let i = currentYear - 10; i <= currentYear + 10; i++) {
        years.push(i + '年')
      }

      // 生成月份范围（1-12月）
      const months = []
      for (let i = 1; i <= 12; i++) {
        months.push(i + '月')
      }

      // 生成当前月的日期范围（动态计算，初始化时使用当前月）
      const days = this.getDaysInMonth(currentYear, new Date().getMonth() + 1)

      // 生成小时范围（0-23时）
      const hours = []
      for (let i = 0; i <= 23; i++) {
        hours.push(String(i).padStart(2, '0') + '时')
      }

      // 生成分钟范围（0-59分）
      const minutes = []
      for (let i = 0; i <= 59; i++) {
        minutes.push(String(i).padStart(2, '0') + '分')
      }

      // 设置选择器的选项数组
      this.pickerRange = [years, months, days, hours, minutes]
    },

    /**
     * 获取指定年月的天数
     * 正确处理闰年和不同月份的天数差异
     * @param {number} year - 年份
     * @param {number} month - 月份（1-12）
     * @returns {Array} 该月所有日期的数组
     */
    getDaysInMonth(year, month) {
      // 使用Date构造函数的特性：new Date(year, month, 0) 返回上个月的最后一天
      // 即当前月的天数
      const daysInMonth = new Date(year, month, 0).getDate()
      const days = []
      for (let i = 1; i <= daysInMonth; i++) {
        days.push(i + '日')
      }
      return days
    },

    /**
     * 根据当前value值初始化选择器的选中状态
     * 如果value无效，则设置为当前时间
     */
    initPickerValue() {
      if (this.value) {
        try {
          // 解析传入的日期字符串
          const date = new Date(this.value)
          if (isNaN(date.getTime())) {
            // 日期无效时使用当前时间
            this.setCurrentTime()
            return
          }

          // 提取日期时间各部分
          const year = date.getFullYear()
          const month = date.getMonth() + 1
          const day = date.getDate()
          const hour = date.getHours()
          const minute = date.getMinutes()

          // 在选项数组中找到对应的索引
          const yearIndex = this.pickerRange[0].findIndex(item => item === year + '年')
          const monthIndex = month - 1  // 月份索引从0开始
          const dayIndex = day - 1       // 日期索引从0开始
          const hourIndex = hour         // 小时索引就是小时值
          const minuteIndex = minute     // 分钟索引就是分钟值

          // 设置选择器的当前值
          this.pickerValue = [
            yearIndex >= 0 ? yearIndex : 10, // 如果年份不在范围内，默认选择中间的年份
            monthIndex,
            dayIndex,
            hourIndex,
            minuteIndex
          ]

          // 根据选择的年月更新日期选项
          this.updateDaysRange()
        } catch (e) {
          console.warn('初始化选择器值失败:', e)
          this.setCurrentTime()
        }
      } else {
        // 没有初始值时使用当前时间
        this.setCurrentTime()
      }
    },

    /**
     * 设置选择器为当前时间
     * 用于初始化或重置选择器状态
     */
    setCurrentTime() {
      const now = new Date()
      const currentYear = now.getFullYear()

      // 在年份选项中找到当前年份的索引
      const yearIndex = this.pickerRange[0].findIndex(item => item === currentYear + '年')

      // 设置为当前时间的各个部分
      this.pickerValue = [
        yearIndex >= 0 ? yearIndex : 10, // 当前年份索引，找不到时默认中间位置
        now.getMonth(),                  // 当前月份索引（0-11）
        now.getDate() - 1,              // 当前日期索引（0开始，所以减1）
        now.getHours(),                 // 当前小时
        now.getMinutes()                // 当前分钟
      ]
    },

    /**
     * 根据当前选择的年月更新日期选项
     * 处理不同月份天数不同的情况，包括闰年
     */
    updateDaysRange() {
      // 获取当前选择的年份和月份字符串
      const yearStr = this.pickerRange[0][this.pickerValue[0]]
      const monthStr = this.pickerRange[1][this.pickerValue[1]]

      if (yearStr && monthStr) {
        // 解析年份和月份数值
        const year = parseInt(yearStr.replace('年', ''))
        const month = parseInt(monthStr.replace('月', ''))

        // 生成新的日期选项
        const newDays = this.getDaysInMonth(year, month)
        this.pickerRange[2] = newDays

        // 如果当前选择的日期超出了新月份的天数，调整到该月最后一天
        // 例如：从1月31日切换到2月时，自动调整为2月28日（或29日）
        if (this.pickerValue[2] >= newDays.length) {
          this.pickerValue[2] = newDays.length - 1
        }
      }
    },

    /**
     * 处理picker-view滚动选择事件
     * 当用户滚动选择器时触发
     * @param {Object} e - 事件对象，包含当前选中的值数组
     */
    onPickerViewChange(e) {
      const oldValue = [...this.pickerValue]
      this.pickerValue = e.detail.value

      // 检查年份或月份是否发生变化
      // 如果变化了，需要重新计算该月的天数
      if (oldValue[0] !== this.pickerValue[0] || oldValue[1] !== this.pickerValue[1]) {
        this.updateDaysRange()
      }
    },

    /**
     * 打开日期选择器弹窗
     * 重新初始化选择器状态并显示弹窗
     */
    openPicker() {
      // 重新初始化选择器的值，确保显示正确的当前值
      this.initPickerValue()
      // 显示弹窗
      this.showPicker = true
      // 触发open事件，通知父组件
      this.$emit('open')
    },

    /**
     * 关闭日期选择器弹窗
     * 隐藏弹窗但不保存选择结果
     */
    closePicker() {
      this.showPicker = false
      // 触发close事件，通知父组件
      this.$emit('close')
    },

    /**
     * 确认选择并返回结果
     * 将选择器的值转换为标准日期时间格式并触发相应事件
     */
    confirmSelection() {
      // 获取当前选择的年月日字符串
      const yearStr = this.pickerRange[0][this.pickerValue[0]]
      const monthStr = this.pickerRange[1][this.pickerValue[1]]
      const dayStr = this.pickerRange[2][this.pickerValue[2]]

      // 验证必要的日期部分是否存在
      if (!yearStr || !monthStr || !dayStr) {
        console.warn('日期选择不完整，关闭选择器')
        this.closePicker()
        return
      }

      // 解析年月日数值
      const year = parseInt(yearStr.replace('年', ''))
      const month = parseInt(monthStr.replace('月', ''))
      const day = parseInt(dayStr.replace('日', ''))

      // 构建基础日期字符串 YYYY-MM-DD
      let dateString = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`

      // 如果是datetime类型，添加时间部分
      if (this.type === 'datetime') {
        const hourStr = this.pickerRange[3] && this.pickerRange[3][this.pickerValue[3]]
        const minuteStr = this.pickerRange[4] && this.pickerRange[4][this.pickerValue[4]]

        if (hourStr && minuteStr) {
          const hour = parseInt(hourStr.replace('时', ''))
          const minute = parseInt(minuteStr.replace('分', ''))
          // 添加时间部分，格式：HH:mm:ss
          dateString += ` ${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}:00`
        } else {
          // 如果时间部分缺失，默认为00:00:00
          dateString += ' 00:00:00'
        }
      }

      // 触发input事件（用于v-model双向绑定）
      this.$emit('input', dateString)
      // 触发change事件（用于监听值变化）
      this.$emit('change', dateString)
      // 关闭选择器
      this.closePicker()
    }
  }
}
</script>

<!--
  组件样式定义
  使用scoped确保样式只作用于当前组件
  使用scss语法支持嵌套和变量
-->
<style lang="scss" scoped>
/* 组件根容器 */
.datetime-picker-fix {
  width: 100%;
}

/*
  输入框样式
  模拟原生input的外观，提供良好的用户体验
*/
.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  height: 70rpx;
  background-color: #f8f9fc;  /* 浅灰背景，与表单风格一致 */
  border-radius: 8rpx;        /* 圆角边框 */
  border: 1px solid #e5e7eb;  /* 浅色边框 */
  cursor: pointer;            /* 鼠标悬停时显示手型 */
  transition: all 0.2s ease;  /* 平滑过渡效果 */

  /* 悬停效果 */
  &:hover {
    border-color: #007AFF;
    background-color: #f0f9ff;
  }

  /* 输入框文本 */
  .picker-text {
    flex: 1;
    font-size: 28rpx;
    color: #333;
    line-height: 1.4;

    /* 占位符状态的样式 */
    &.placeholder {
      color: #999;
      font-style: italic;
    }
  }

  /* 日历图标 */
  .picker-icon {
    font-size: 28rpx;
    color: #666;
    margin-left: 10rpx;
    transition: color 0.2s ease;
  }

  /* 激活状态 */
  &:active {
    transform: scale(0.98);
  }
}

/*
  弹窗遮罩层
  覆盖整个屏幕，提供模态对话框效果
  关键解决方案：使用高z-index确保在所有元素之上显示
*/
.picker-overlay {
  position: fixed;           /* 固定定位，相对于视窗 */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);  /* 半透明黑色遮罩 */
  z-index: 10000;           /* 高层级，确保在最顶层显示 */
  display: flex;
  align-items: flex-end;    /* 内容对齐到底部 */
  animation: fadeIn 0.3s ease;  /* 淡入动画 */
}

/*
  弹窗容器
  底部弹出的白色容器，符合移动端设计规范
*/
.picker-container {
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;  /* 顶部圆角 */
  overflow: hidden;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);  /* 向上的阴影 */
  animation: slideUp 0.3s ease;  /* 向上滑入动画 */
  max-height: 80vh;          /* 最大高度限制 */
}

/*
  弹窗头部
  包含标题和操作按钮的区域
*/
.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1px solid #f0f0f0;  /* 底部分割线 */
  background-color: #fafafa;         /* 浅灰背景区分内容区 */

  /* 取消和确定按钮 */
  .picker-cancel,
  .picker-confirm {
    font-size: 28rpx;
    color: #007AFF;        /* iOS风格的蓝色 */
    padding: 10rpx 20rpx;
    border-radius: 6rpx;
    transition: all 0.2s ease;
    cursor: pointer;

    /* 按钮悬停效果 */
    &:hover {
      background-color: rgba(0, 122, 255, 0.1);
    }

    /* 按钮按下效果 */
    &:active {
      transform: scale(0.95);
      background-color: rgba(0, 122, 255, 0.2);
    }
  }

  /* 弹窗标题 */
  .picker-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    text-align: center;
  }
}

/*
  选择器视图容器
  包含所有滚动选择列的区域
*/
.picker-view-container {
  height: 400rpx;           /* 固定高度，显示约5个选项 */
  padding: 20rpx 0;
  background-color: #fff;
}

/*
  选择器选项样式
  每个可选择项的样式定义
*/
.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;            /* 每个选项的高度 */
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  transition: all 0.2s ease;

  /* 选中状态的视觉反馈 */
  &:hover {
    background-color: rgba(0, 122, 255, 0.05);
  }
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
</style>
