.viewport {
  height: 100%;
  background: url("@/static/images/page_bg.png") no-repeat center top;
  background-size: contain;
}
.container {
  padding: 30rpx;
}
.page-container {
  padding: 136rpx 24rpx 30rpx;
}
.page-title {
  font-weight: bold;
  font-size: 36rpx;
  color: #FFF;
  text-align: center;
}
.image {
  width: 100%;
  height: 100%;
}
.box {
  padding: 30rpx;
  background: #fff;
  border-radius: 10rpx;
  margin-bottom: 30rpx;
  .info-list {
    padding: 20rpx 0;
    font-size: 28rpx;
    color: #333;
    .item {
      padding: 16rpx 0;
      display: flex;
      align-items: center;
      line-height: 150%;
      &.multi-line {
        align-items: flex-start;
      }
    }
    .name {
      flex: 0 0 170rpx;
      text-align: right;
      color: #BBB;
      margin-right: 20rpx;
    }
  }
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.search-box {
  margin: 30rpx 0;
  .search-item {
    width: 100%;
    height: 80rpx;
    display: flex;
    align-items: center;
    background: #FFF;
    border-radius: 10rpx;
    font-size: 28rpx;
    margin-bottom: 20rpx;
    .icon {
      width: 32rpx;
      height: 32rpx;
      margin: 0 16rpx;
    }
    .uni-icon {
      margin-left: 10rpx;
    }
    .uni-select {
      border: 0;
    }
    .uni-input-placeholder,
    .uni-select__input-placeholder {
      color: #BBB;
      font-size: 28rpx;
    }
  }
}
.status-box {
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;
    .circle-icon {
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
      margin-right: 10rpx;
      .icon {
        width: 30rpx;
        height: 30rpx;
      }
    }
    .content {
      display: inline-flex;
      align-items: center;
      font-size: 30rpx;
    }
    .go-detail {
      display: inline-flex;
      align-items: center;
      font-size: 28rpx;
      line-height: 22px;
      color: #BBB;
    }
  }
  &.green {
    background: linear-gradient( 360deg, #FFFFFF 0%, #DEFAED 100%), #FFF;
    background-size: 100% 160rpx;
    background-repeat: no-repeat;
    .title {
      .circle-icon {
        background: #2BA97B;
      }
      .content {
        color: #2BA97B;
      }
    }
  }
  &.red {
    background: linear-gradient( 180deg, #FFEBEB 0%, #FFFFFF 100%), #FFF;
    background-size: 100% 160rpx;
    background-repeat: no-repeat;
    .title {
      .circle-icon {
        background: #EA6055;
      }
      .content {
        color: #EA6055;
      }
    }
  }
}