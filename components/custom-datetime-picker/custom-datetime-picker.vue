<template>
  <view class="custom-datetime-picker">
    <view class="picker-input" @click="openPicker">
      <text class="picker-text" :class="{ 'placeholder': !displayValue }">
        {{ displayValue || placeholder }}
      </text>
      <text class="picker-icon">📅</text>
    </view>
    
    <!-- 移动端日期选择器 -->
    <view v-if="showPicker" class="picker-mask" @click="closePicker">
      <view class="picker-popup" @click.stop>
        <view class="picker-header">
          <text class="picker-title">{{ title }}</text>
          <text class="picker-close" @click="closePicker">✕</text>
        </view>
        
        <view class="picker-content">
          <picker-view 
            :value="pickerValue" 
            @change="onPickerChange"
            class="picker-view"
          >
            <!-- 年份 -->
            <picker-view-column>
              <view v-for="(year, index) in years" :key="index" class="picker-item">
                {{ year }}年
              </view>
            </picker-view-column>
            
            <!-- 月份 -->
            <picker-view-column>
              <view v-for="(month, index) in months" :key="index" class="picker-item">
                {{ month }}月
              </view>
            </picker-view-column>
            
            <!-- 日期 -->
            <picker-view-column>
              <view v-for="(day, index) in days" :key="index" class="picker-item">
                {{ day }}日
              </view>
            </picker-view-column>
            
            <!-- 小时 -->
            <picker-view-column v-if="type === 'datetime'">
              <view v-for="(hour, index) in hours" :key="index" class="picker-item">
                {{ hour }}时
              </view>
            </picker-view-column>
            
            <!-- 分钟 -->
            <picker-view-column v-if="type === 'datetime'">
              <view v-for="(minute, index) in minutes" :key="index" class="picker-item">
                {{ minute }}分
              </view>
            </picker-view-column>
          </picker-view>
        </view>
        
        <view class="picker-footer">
          <button class="picker-btn cancel-btn" @click="closePicker">取消</button>
          <button class="picker-btn confirm-btn" @click="confirmPicker">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CustomDatetimePicker',
  props: {
    value: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'datetime' // 'date' 或 'datetime'
    },
    placeholder: {
      type: String,
      default: '请选择日期时间'
    },
    title: {
      type: String,
      default: '选择日期时间'
    }
  },
  data() {
    return {
      showPicker: false,
      pickerValue: [0, 0, 0, 0, 0],
      years: [],
      months: [],
      days: [],
      hours: [],
      minutes: [],
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth() + 1,
      currentDay: new Date().getDate(),
      currentHour: new Date().getHours(),
      currentMinute: new Date().getMinutes()
    }
  },
  computed: {
    displayValue() {
      if (!this.value) return ''
      
      try {
        const date = new Date(this.value)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        
        if (this.type === 'datetime') {
          const hour = String(date.getHours()).padStart(2, '0')
          const minute = String(date.getMinutes()).padStart(2, '0')
          return `${year}-${month}-${day} ${hour}:${minute}`
        } else {
          return `${year}-${month}-${day}`
        }
      } catch (e) {
        return this.value
      }
    }
  },
  mounted() {
    this.initPickerData()
    this.initPickerValue()
  },
  methods: {
    initPickerData() {
      // 初始化年份（当前年份前后10年）
      const currentYear = new Date().getFullYear()
      this.years = []
      for (let i = currentYear - 10; i <= currentYear + 10; i++) {
        this.years.push(i)
      }
      
      // 初始化月份
      this.months = []
      for (let i = 1; i <= 12; i++) {
        this.months.push(i)
      }
      
      // 初始化小时
      this.hours = []
      for (let i = 0; i <= 23; i++) {
        this.hours.push(i)
      }
      
      // 初始化分钟
      this.minutes = []
      for (let i = 0; i <= 59; i++) {
        this.minutes.push(i)
      }
      
      this.updateDays()
    },
    
    updateDays() {
      const year = this.years[this.pickerValue[0]]
      const month = this.months[this.pickerValue[1]]
      const daysInMonth = new Date(year, month, 0).getDate()
      
      this.days = []
      for (let i = 1; i <= daysInMonth; i++) {
        this.days.push(i)
      }
    },
    
    initPickerValue() {
      if (this.value) {
        try {
          const date = new Date(this.value)
          const year = date.getFullYear()
          const month = date.getMonth() + 1
          const day = date.getDate()
          const hour = date.getHours()
          const minute = date.getMinutes()
          
          this.pickerValue = [
            this.years.indexOf(year),
            this.months.indexOf(month),
            day - 1,
            hour,
            minute
          ]
        } catch (e) {
          this.setCurrentTime()
        }
      } else {
        this.setCurrentTime()
      }
      this.updateDays()
    },
    
    setCurrentTime() {
      const now = new Date()
      this.pickerValue = [
        this.years.indexOf(now.getFullYear()),
        this.months.indexOf(now.getMonth() + 1),
        now.getDate() - 1,
        now.getHours(),
        now.getMinutes()
      ]
    },
    
    onPickerChange(e) {
      this.pickerValue = e.detail.value
      this.updateDays()
      
      // 如果选择的日期超出了当月的天数，调整到当月最后一天
      if (this.pickerValue[2] >= this.days.length) {
        this.pickerValue[2] = this.days.length - 1
      }
    },
    
    openPicker() {
      this.initPickerValue()
      this.showPicker = true
      this.$emit('open')
    },
    
    closePicker() {
      this.showPicker = false
      this.$emit('close')
    },
    
    confirmPicker() {
      const year = this.years[this.pickerValue[0]]
      const month = this.months[this.pickerValue[1]]
      const day = this.days[this.pickerValue[2]]
      
      let dateString = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
      
      if (this.type === 'datetime') {
        const hour = this.hours[this.pickerValue[3]]
        const minute = this.minutes[this.pickerValue[4]]
        dateString += ` ${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}:00`
      }
      
      this.$emit('input', dateString)
      this.$emit('change', dateString)
      this.closePicker()
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-datetime-picker {
  width: 100%;
}

.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  height: 70rpx;
  background-color: #f8f9fc;
  border-radius: 8rpx;
  border: 1px solid #e5e7eb;
  
  .picker-text {
    flex: 1;
    font-size: 28rpx;
    color: #333;
    
    &.placeholder {
      color: #999;
    }
  }
  
  .picker-icon {
    font-size: 28rpx;
    color: #666;
  }
}

.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: flex-end;
}

.picker-popup {
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1px solid #f0f0f0;
  
  .picker-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }
  
  .picker-close {
    font-size: 36rpx;
    color: #999;
    padding: 10rpx;
  }
}

.picker-content {
  padding: 20rpx 0;
}

.picker-view {
  height: 400rpx;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
}

.picker-footer {
  display: flex;
  padding: 30rpx 40rpx;
  border-top: 1px solid #f0f0f0;
  gap: 20rpx;
  
  .picker-btn {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    border-radius: 8rpx;
    font-size: 28rpx;
    border: none;
    
    &.cancel-btn {
      background-color: #f5f5f5;
      color: #666;
    }
    
    &.confirm-btn {
      background-color: #007AFF;
      color: #fff;
    }
  }
}
</style>
