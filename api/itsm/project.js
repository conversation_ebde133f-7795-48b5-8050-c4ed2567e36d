import request from '@/utils/request'

// 分页查询
export function getProjects(query) {
    return request({
        url: '/itsm/projectInfo/list',
        headers: {
            isToken: true,
        },
        method: 'get',
        params: query
    })
}

// 获取当前系统所有项目及其下所属服务器，系统，人员
export function getAllProjects(query) {
    return request({
        url: '/itsm/projectInfo/getItsmAllProjectInfo',
        headers: {
            isToken: true,
        },
        method: 'get',
        params: query
    })
}