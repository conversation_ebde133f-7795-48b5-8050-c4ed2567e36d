<template>
  <view class="viewport ticket">
    <view class="page-container">
      <view class="page-title">工单情况</view>
      <view class="search-box">
        <uni-row :gutter="10">
          <uni-col :span="12">
            <view class="search-item">
              <!-- <image class="icon" mode="aspectFit" src="@/static/images/icons/search.png" /> -->
              <uni-icons class="uni-icon" type="search" color="#BBB" size="22"></uni-icons>
              <view class="search-input">
                <uni-combox :border="false" :candidates="persons" placeholder="工单编号/名称/处理人"></uni-combox>
              </view>
            </view>
          </uni-col>
          <uni-col :span="12">
            <view class="search-item">
              <uni-data-select
                v-model="searchParams.status"
                placeholder="状态"
                :border="false" 
                :localdata="range"
                @change="change"
              ></uni-data-select>
            </view>
          </uni-col>
          <uni-col :span="12">
            <view class="search-item">
              <view class="search-input">
                <uni-datetime-picker
                  :border="false"
                  v-model="searchParams.date"
                  type="daterange"
                  start="2021-3-20"
                  end="2021-5-20"
                  rangeSeparator="至"
                />
              </view>
            </view>
          </uni-col>
          <uni-col :span="12">
            <view class="search-item">
              <uni-data-select
                v-model="searchParams.department"
                placeholder="部门"
                :border="false" 
                :localdata="range"
                @change="change"
              ></uni-data-select>
            </view>
          </uni-col>
        </uni-row>
      </view>
      <view class="box" :class="item.status" v-for="(item, index) in statusList" :key="index">
        <view class="title">
          <view class="content">
            <image v-if="item.status === 'pending'" class="icon" mode="aspectFit" src="@/static/images/icons/clock2.png" />
            <image v-if="item.status === 'processing'" class="icon" mode="aspectFit" src="@/static/images/icons/transfer.png" />
            <image v-if="item.status === 'timeout'" class="icon" mode="aspectFit" src="@/static/images/icons/warning2.png" />
            <image v-if="item.status === 'completed'" class="icon" mode="aspectFit" src="@/static/images/icons/check.png" />
            <image v-if="item.status === 'cancelled'" class="icon" mode="aspectFit" src="@/static/images/icons/close.png" />
            <text class="text">{{ item.text }}</text>
          </view>
          <view class="extra">
            <text class="date">{{ currentDate }}</text>
          </view>
        </view>
        <view class="body">
          <view class="info-list">
            <view class="item">
              <view class="name">
                <text class="text">工单编号</text>
              </view>
              <view class="value">
                <text class="text">ZSJ0001</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">工单名称</text>
              </view>
              <view class="value">
                <text class="text">XXX工作任务内容文字</text>
              </view>
            </view>
            <view class="item">
              <view class="name">
                <text class="text">部门/处理人</text>
              </view>
              <view class="value">
                <text class="text">京信股份  张三</text>
              </view>
            </view>
            <view class="item multi-line">
              <view class="name">
                <text class="text">任务内容</text>
              </view>
              <view class="value">
                <text class="text">处理XX系统的数据，形成统计数限制形成限制形成统计字数限制数形成统计数限制形成限制形成统计字数限制数</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  name: 'PersonPage',
  data() {
    return {
      // 当前日期，格式：YYYY-MM-DD
      currentDate: '',
      // statusList: ['pending', 'processing', 'timeout', 'completed', 'cancelled'],
      statusList: [
        { status: 'pending', text: '待受理' },
        { status: 'processing', text: '处理中' },
        { status: 'timeout', text: '超时' },
        { status: 'completed', text: '已完成' },
        { status: 'cancelled', text: '已取消' }
      ],
      persons: ['张三', '李四', '王五', '赵六'],
      range: [
        { text: '选项0', value: 0 },
        { text: '选项1', value: 1 },
        { text: '选项2', value: 2 }
      ],
      searchParams: {
        name: '',
        status: undefined,
        date: '',
        department: ''
      }
    }
  },

  // 生命周期方法
  mounted() {
    // 组件加载时初始化当前日期
    this.initCurrentDate()
  },

  // 组件方法
  methods: {
    /**
     * 初始化当前日期
     * 格式化为 YYYY-MM-DD 格式
     */
    initCurrentDate() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')

      this.currentDate = `${year}-${month}-${day}`

      console.log('Ticket页面初始化当前日期:', this.currentDate)
    }
  }
}
</script>
<style lang="scss">
@import url("@/static/scss/dashboard.scss");

.ticket{
  .box {
    padding: 0;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 68rpx;
      .content {
        display: inline-flex;
        align-items: center;
        color: #FFF;
        padding: 0 10rpx 10rpx 10rpx;
      }
      .icon {
        width: 36rpx;
        height: 36rpx;
        margin-right: 10rpx;
      }
      .date {
        color: #BBB;
        padding: 0 30rpx;
      }
    }
    .body {
      padding: 10rpx 24rpx;
    }
    .info-list {
      .multi-line {
        .value {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2; /* 限制显示2行 */
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    &.pending {
      .title {
        background: url("@/static/images/dashboard/title_bg_01.png") no-repeat left center;
        background-size: contain;
      }
    }
    &.processing {
      .title {
        background: url("@/static/images/dashboard/title_bg_02.png") no-repeat left center;
        background-size: contain;
      }
    }
    &.timeout {
      .title {
        background: url("@/static/images/dashboard/title_bg_03.png") no-repeat left center;
        background-size: contain;
      }
    }
    &.completed {
      .title {
        background: url("@/static/images/dashboard/title_bg_04.png") no-repeat left center;
        background-size: contain;
      }
    }
    &.cancelled {
      .title {
        background: url("@/static/images/dashboard/title_bg_05.png") no-repeat left center;
        background-size: contain;
      }
    }
  }
}
</style>