<template>
  <view class="page-container">

    <!-- 内容区域 -->
    <scroll-view
        :class="['content', { 'picker-visible': isPickerVisible }]"
        scroll-y=""
        @scrolltolower="loadMore"
        :refresher-triggered="isRefreshing"
        @refresherrefresh="onRefresh"
        refresher-enabled="true"
        lower-threshold="50"
    >
      <!-- 请假页面 -->
      <view v-if="activeTab === 0" class="leave-form">
        <uni-forms ref="form" :model="user" :rules="rules" class="form-container">
          <uni-forms-item name="leaveType" label="请假类型" label-width="100px">
            <picker :range="leaveTypes" @change="onLeaveTypeChange" :value="user.leaveType">
              <view class="uni-input">{{ leaveTypes[user.leaveType] || '请选择请假类型' }}</view>
            </picker>
          </uni-forms-item>
          <uni-forms-item name="startDate" label="开始时间" label-width="100px">
            <uni-datetime-picker
                type="datetime"
                v-model="user.startDate"
                @change="onStartDateChange"
                @open="togglePickerVisibility(true)"
                @close="togglePickerVisibility(false)"
                :clear-icon="false"
                :border="false"
                class="custom-datetime-picker"
            />
          </uni-forms-item>
          <uni-forms-item name="endDate" label="结束时间" label-width="100px">
            <uni-datetime-picker
                type="datetime"
                v-model="user.endDate"
                @change="onEndDateChange"
                @open="togglePickerVisibility(true)"
                @close="togglePickerVisibility(false)"
                :clear-icon="false"
                :border="false"
                class="custom-datetime-picker"
            />
          </uni-forms-item>
          <uni-forms-item name="attachment" label="附件" label-width="100px" class="file-upload-item">
            <uni-file-picker 
              v-model="user.attachment" 
              fileMediatype="image" 
              :limit="1" 
              @select="onFileSelect"
              mode="grid" 
              class="file-picker"
            >
              <template #default>
                <view class="upload-button">
                  <button>上传附件</button>
                </view>
              </template>
            </uni-file-picker>
          </uni-forms-item>
          
          <view class="form-actions">
            <button type="primary" @click="submit" class="submit-button">提交申请</button>
          </view>
        </uni-forms>
      </view>

      <!-- 查看历史页面 -->
      <view v-else class="history-list">
        <view v-for="(item, index) in leaveHistory" :key="index" class="record-item">
          <view class="record-content">
            <view class="record-main">
              <view class="record-header">
                <view class="user-info">
                  <text class="user-name">{{ item.userName }}</text>
                  <text class="time-duration">{{ calculateDuration(item.startTime, item.endTime) }}</text>
                </view>
<!--                <view class="record-status" :class="getStatusClass(item)">-->
<!--                  {{ getStatusText(item.processingStatus) }}-->
<!--                </view>-->
              </view>
              
              <view class="record-details">
                <view class="detail-row">
                  <text class="detail-label">请假类型：</text>
                  <text class="detail-value">{{ getLeaveType(item.type) }}</text>
                </view>
                <view class="detail-row">
                  <text class="detail-label">开始时间：</text>
                  <text class="detail-value">{{ formatDateTime(item.startTime) }}</text>
                </view>
                <view class="detail-row">
                  <text class="detail-label">结束时间：</text>
                  <text class="detail-value">{{ formatDateTime(item.endTime) }}</text>
                </view>
                <view class="detail-row reason-row" v-if="item.reason">
                  <text class="detail-label">请假事由：</text>
                  <text class="detail-value reason-text">{{ item.reason }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="!leaveHistory.length" class="empty-state">
          <text class="empty-text">暂无请假记录</text>
        </view>

        <!-- 加载更多状态 -->
        <uni-load-more :status="loadingMore ? 'loading' : (pageNo >= totalPages ? 'noMore' : 'more')"></uni-load-more>
      </view>

    </scroll-view>

    <!-- TabBar -->
    <view v-if="showTabBar" class="custom-tab-bar">
      <view
          class="tab-item"
          :class="{ active: activeTab === 0 }"
          @click="switchTab(0)"
      >
        <view class="tab-icon">📝</view>
        <text class="tab-text">申请请假</text>
      </view>
      <view
          class="tab-item"
          :class="{ active: activeTab === 1 }"
          @click="switchTab(1)"
      >
        <view class="tab-icon">📋</view>
        <text class="tab-text">查看记录</text>
      </view>
    </view>

  </view>
</template>

<script>

import {addLeave, list, uploadFile} from "@/api/itsm/leave";
import {completeTask, startWorkFlow} from "@/api/itsm/workOvertime";

export default {
  data() {
    return {
      leaveTypes: ['请选择', '年假', '事假', '病假', '婚假', '调休'],
      leaveTypeMapping: {
        '年假': 1,
        '事假': 2,
        '病假': 3,
        '婚假': 4,
        '调休': 5
      },
      user: {
        leaveType: undefined,
        startDate: '',
        endDate: '',
        attachment: [],
        fileIds: [], // 存储文件 ID
        fileId: ''
      },
      rules: {
        leaveType: {
          rules: [{
            required: true,
            errorMessage: '请假类型不能为空'
          }]
        },
        startDate: {
          rules: [{
            required: true,
            errorMessage: '开始时间不能为空'
          }]
        },
        endDate: {
          rules: [{
            required: true,
            errorMessage: '结束时间不能为空'
          }]
        }
      },
      maxAttachmentCount: 3, // 限制最多上传的图片数量

      activeTab: 0, // 标签页索引
      listData: [], // 列表数据


      historyList: [],
      loading: false,
      noMoreData: false,
      showTabBar: true, // 控制 TabBar 显示或隐藏

      isPickerVisible: false, // 新增状态，用于控制 Picker 是否可见

      leaveHistory: [], // 存储请假历史记录
      pageNo: 1, // 当前页码
      pageSize: 10, // 每页显示条目数
      totalPages: 0, // 总页数
      isRefreshing: false, // 是否正在刷新
      loadingMore: false, // 是否正在加载更多

      leaveConverTypes: {
        '1': '年假',
        '2': '事假',
        '3': '病假',
        '4': '婚假',
        '5': '调休'
      }

    }
  },
  mounted() {
    this.$refs.form.setRules(this.rules)
  },

  watch: {
    activeTab(newValue) {
      if (newValue === 1) {
        // 如果切换到历史记录页，则重新获取第一页的数据
        this.onRefresh();
      }
    }
  },

  methods: {
    // 获取请假历史记录
    async fetchLeaveHistory(pageNo = this.pageNo) {
      const queryParams = {pageNum: 1}
      list(queryParams).then(response => {
            console.log('获取后端分页请假记录结果：', JSON.stringify(response, null, 6))
            if (response.code === 200){
              const { rows, total } = response;
              this.leaveHistory =  rows;
              console.log('this.leaveHistory :' + JSON.stringify(this.leaveHistory,null,6));
              this.totalPages = Math.ceil(total / this.pageSize);
            }
          }
      );
    },

    onRefresh() {
      this.isRefreshing = true;
      this.pageNo = 1;
      this.fetchLeaveHistory().finally(() => {
        this.isRefreshing = false;
      });
    },

    // 上拉加载更多
    loadMore() {
      if (this.loadingMore || this.pageNo >= this.totalPages) return;
      this.loadingMore = true;
      this.pageNo++;
      this.fetchLeaveHistory(this.pageNo).finally(() => {
        this.loadingMore = false;
      });
    },

    onLeaveTypeChange(e) {
      // console.log('输出一下请假类型：' + JSON.stringify(e, null, 6))
      // 获取选中值的索引
      this.user.leaveType = Number(e.detail.value); // 更新为索引值
    },

    onStartDateChange(e) {
      this.user.startDate = e
    },

    onEndDateChange(e) {
      this.user.endDate = e
    },

    onFileSelect(e) {
      console.log('Selected files:', JSON.stringify(e.tempFiles, null, 6))

      // 检查当前已上传文件数量
      const currentCount = this.user.attachment.length;
      const selectedFiles = e.tempFiles;
      // 如果总数超过限制
      if (currentCount + selectedFiles.length > this.maxAttachmentCount) {
        const allowedCount = this.maxAttachmentCount - currentCount;
        uni.showToast({
          title: `最多上传${this.maxAttachmentCount}张图片，您已超过限制！`,
          icon: 'none',
          duration: 2000
        });
        // 仅添加允许的文件数量
        this.user.attachment = [
          ...this.user.attachment,
          ...selectedFiles.slice(0, allowedCount).map(file => ({url: file.path}))
        ];
      } else {
        // 转换上传文件为符合 uni-file-picker 的格式
        const selectedFiles = e.tempFiles.map(file => ({
          url: file.path // uni-file-picker 需要 { url: '图片路径' }
        }));
        // 合并已上传文件
        this.user.attachment = [...this.user.attachment, ...selectedFiles];

        for (const file of e.tempFiles) {
          let data = {
            fileType: 'leaveInfo',
            filePath: file.path, // 本地文件路径
            name: 'file', // 后端接收文件的字段名
          }
          uploadFile(data).then(response => {
            console.log('APP端上传请假附件结果：' + JSON.stringify(response, null, 6))
            if (response.code === 200) { // 假设后端返回的成功码是 200
              // this.user.fileIds.push(response.msg); // 将 fileId 添加到 user.fileIds 中
              this.user.fileId = response.msg;
              uni.showToast({title: "文件上传成功", icon: 'success'});
            } else {
              uni.showToast({title: "文件上传失败", icon: 'none'});
            }
          })
        }
      }
    },

    switchTab(tabIndex) {
      this.activeTab = tabIndex;
      if (tabIndex === 1) {
        this.onRefresh();
      }
    },

    getLeaveType(type) {
      return this.leaveConverTypes[type] || '未知';
    },

    togglePickerVisibility(visible) {
      this.isPickerVisible = visible;
    },

    submit() {
      this.$refs.form.validate().then(() => {
        const leaveRequest = {
          type: this.leaveTypeMapping[this.leaveTypes[this.user.leaveType]], // 使用映射获取对应值
          startTime: this.user.startDate,
          endTime: this.user.endDate,
          fileId: this.user.fileId,
          processingStatus: 'draft'
        };

        console.log('输出一下，传递到后端的请假申请表单参数：' + JSON.stringify(leaveRequest, null, 6));

        addLeave(leaveRequest).then(res => {
          console.log('APP端提交请假结果：' + JSON.stringify(res, null, 6));

          if (res.code === 200) {
            const submitFormData = {
              businessKey: res.data.leaveId,
              tableName: 'itsm_attendance_leave_log',
              variables: {
                entity: res.data,
                userList: [1, 3],
                userList2: [1, 3]
              }
            };
            //启动审批流
            startWorkFlow(submitFormData).then(workflowRes => {
              if (workflowRes.code === 200) {
                const taskForm = {
                  taskId: workflowRes.data.taskId,
                  taskVariables: {},
                  messageType: ['1'],
                  wfCopyList: []
                };

                completeTask(taskForm).then(taskRes => {
                  console.log('app端提交加班申请，工作流提交结果：' + JSON.stringify(taskRes, null, 6));

                  // 所有异步操作完成后才显示成功消息并返回
                  uni.showToast({ title: "提交成功", icon: 'success', duration: 2000 }); // 设置持续时间以确保提示可见

                  // 在成功消息显示一定时间后返回上一页
                  setTimeout(() => {
                    uni.navigateBack(); // 返回上一页
                  }, 2000); // 等待2秒再返回，确保提示框显示完毕
                }).catch(err => {
                  console.error('完成任务失败', err);
                  uni.showToast({ title: "提交失败", icon: 'none' });
                });
              } else {
                console.error('启动工作流失败', workflowRes);
                uni.showToast({ title: "提交失败", icon: 'none' });
              }
            }).catch(err => {
              console.error('启动工作流失败', err);
              uni.showToast({ title: "提交失败", icon: 'none' });
            });


            // 显示成功消息并设置持续时间为 2 秒
            uni.showToast({
              title: "提交成功",
              icon: 'success',
              duration: 2000 // 设置适当的时间，如2秒
            });

            // 清空表单数据
            this.user = {};

            // 延迟导航回上一页，确保消息框显示完毕
            setTimeout(() => {
              uni.navigateBack(); // 返回上一页
            }, 2000); // 等待2秒再返回，确保提示框显示完毕
          } else {
            uni.showToast({ title: "提交失败", icon: 'none' });
          }
        }).catch(err => {
          console.error('提交请假请求失败', err);
          uni.showToast({ title: "提交失败", icon: 'none' });
        });
      }).catch(err => {
        console.log('表单校验失败', err);
      });
    },

    // 计算时长
    calculateDuration(startTime, endTime) {
      if (!startTime || !endTime) return '未知时长';
      try {
        const start = new Date(startTime);
        const end = new Date(endTime);
        const hours = Math.round((end - start) / (1000 * 60 * 60));
        return `${hours}小时`;
      } catch (e) {
        return '计算错误';
      }
    },

    // 格式化日期时间
    formatDateTime(dateString) {
      if (!dateString) return '未设置';
      try {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (e) {
        return dateString;
      }
    },

    // 获取状态样式类
    getStatusClass(item) {
      if (!item || !item.processingStatus) return 'status-draft';
      const status = item.processingStatus;
      return `status-${status}`;
    },

    // 获取状态文本
    getStatusText(status) {
      if (!status) return '未知状态';
      const statusMap = {
        'cancel': '已撤销',
        'draft': '草稿',
        'waiting': '待审核',
        'finish': '已完成',
        'invalid': '已作废',
        'back': '已回退',
        'termination': '已终止',
        'inReview': '审核中'
      };
      return statusMap[status] || '未知状态';
    }

  },
}
</script>

<style lang="scss">
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: calc(98rpx + env(safe-area-inset-bottom)); // 适配底部安全区域
}

/* 记录列表样式 */
.history-list {
  padding: 20rpx 24rpx;
  background-color: #f7f8fa;
}

.record-item {
  background-color: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.03);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
  }
}

.record-content {
  .record-main {
    padding: 24rpx;

    .record-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;
      padding-bottom: 16rpx;
      border-bottom: 2rpx solid #f5f7fa;
      
      .user-info {
        display: flex;
        align-items: center;
        gap: 16rpx;
        
        .user-name {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
        }
        
        .time-duration {
          font-size: 24rpx;
          color: #666;
          background-color: #f8f9fc;
          padding: 4rpx 16rpx;
          border-radius: 24rpx;
        }
      }
      
      .record-status {
        font-size: 24rpx;
        padding: 6rpx 20rpx;
        border-radius: 24rpx;
        font-weight: 500;
        
        &.status-finish {
          background-color: rgba(82, 196, 26, 0.08) !important;
          color: #52c41a !important;
        }
        
        &.status-waiting {
          background-color: rgba(24, 144, 255, 0.08) !important;
          color: #1890ff !important;
        }
        
        &.status-draft {
          background-color: rgba(191, 191, 191, 0.08) !important;
          color: #8c8c8c !important;
        }
        
        &.status-invalid {
          background-color: rgba(245, 34, 45, 0.08) !important;
          color: #f5222d !important;
        }
        
        &.status-cancel {
          background-color: rgba(250, 173, 20, 0.08) !important;
          color: #faad14 !important;
        }
        
        &.status-back {
          background-color: rgba(114, 46, 209, 0.08) !important;
          color: #722ed1 !important;
        }
        
        &.status-termination {
          background-color: rgba(207, 19, 34, 0.08) !important;
          color: #cf1322 !important;
        }
        
        &.status-inReview {
          background-color: rgba(250, 173, 20, 0.08) !important;
          color: #faad14 !important;
        }
      }
    }

    .record-details {
      background-color: #fafbfc;
      border-radius: 16rpx;
      padding: 20rpx;
      
      .detail-row {
        display: flex;
        margin-bottom: 16rpx;
        align-items: flex-start;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        &.reason-row {
          margin-top: 16rpx;
          padding-top: 16rpx;
          border-top: 2rpx solid rgba(0,0,0,0.03);
        }

        .detail-label {
          width: 140rpx;
          color: #666;
          font-size: 26rpx;
          flex-shrink: 0;
        }

        .detail-value {
          flex: 1;
          color: #333;
          font-size: 26rpx;
          line-height: 1.5;
          
          &.reason-text {
            word-break: break-all;
            white-space: pre-wrap;
          }
        }
      }
    }
  }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
  
  .empty-text {
    color: #999;
    font-size: 28rpx;
    background-color: #f8f9fc;
    padding: 16rpx 32rpx;
    border-radius: 32rpx;
  }
}

/* 自定义TabBar样式优化 */
.custom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 98rpx; // 适当增加高度，让布局更加舒适
  background-color: rgba(255, 255, 255, 0.98);
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -1rpx 6rpx rgba(0,0,0,0.03);
  backdrop-filter: blur(10px);
  z-index: 100;
  padding-bottom: env(safe-area-inset-bottom);

  .tab-item {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    padding: 8rpx 0; // 增加内边距
    transition: all 0.3s ease;
    
    &.active {
      .tab-icon {
        transform: scale(1.05);
      }
      
      .tab-text {
        color: #007AFF;
        font-weight: 500;
      }
      
      &::after {
        content: '';
        position: absolute;
        bottom: 14rpx; // 调整下划线位置，让它更靠近文字
        left: 50%;
        transform: translateX(-50%);
        width: 28rpx; // 稍微加宽下划线
        height: 3rpx;
        background-color: #007AFF;
        border-radius: 2rpx;
        opacity: 0.8;
      }
    }

    .tab-icon {
      font-size: 36rpx; // 稍微增大图标
      height: 36rpx;
      line-height: 36rpx;
      margin-bottom: 8rpx; // 增加图标和文字的间距
      transition: transform 0.2s ease;
    }

    .tab-text {
      font-size: 24rpx; // 稍微增大文字
      color: #666;
      line-height: 1.2; // 增加行高
      padding-bottom: 4rpx; // 添加文字底部间距
    }
  }
}

/* 相应调整内容区域的底部padding */
.content {
  padding-bottom: calc(98rpx + env(safe-area-inset-bottom));
}

.page-container {
  padding-bottom: calc(98rpx + env(safe-area-inset-bottom));
}

/* 内容区域样式 */
.content {
  flex: 1;
  overflow-y: auto;
  background-color: #f7f8fa;
  padding-bottom: calc(98rpx + env(safe-area-inset-bottom));
  
  &.refreshing {
    transition: transform 0.3s ease;
  }
}

/* 请假表单样式 */
.leave-form {
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.03);

  .form-container {
    padding: 20rpx;
  }

  .uni-input {
    height: 70rpx;
    padding: 0 20rpx;
    background-color: #f8f9fc;
    border-radius: 8rpx;
    font-size: 28rpx;
    display: flex;
    align-items: center;
  }

  .upload-button {
    margin-top: 20rpx;
    
    button {
      width: 100%;
      height: 80rpx;
      line-height: 80rpx;
      font-size: 28rpx;
      border-radius: 8rpx;
    }
  }

  .file-picker {
    width: 100%;
  }

  .form-actions {
    margin-top: 40rpx;
    padding: 0 20rpx;

    .submit-button {
      width: 100%;
      height: 88rpx;
      line-height: 88rpx;
      font-size: 32rpx;
      border-radius: 44rpx;
      background-color: #007AFF;
      color: #fff;
      
      &:active {
        opacity: 0.9;
      }
    }
  }
}

/* uni-forms-item 样式优化 */
:deep(.uni-forms-item) {
  margin-bottom: 24rpx;
  
  .uni-forms-item__label {
    font-size: 28rpx;
    color: #333;
  }
  
  .uni-forms-item__content {
    min-height: 70rpx;
  }
}

/* 日期选择器样式优化 */
:deep(.uni-datetime-picker) {
  .uni-datetime-picker-btn {
    padding: 0 20rpx;
    height: 70rpx;
    background-color: #f8f9fc;
    border-radius: 8rpx;

    .uni-datetime-picker-text {
      font-size: 28rpx;
      color: #333;
    }
  }
}

/* 自定义日期选择器样式 */
.custom-datetime-picker {
  :deep(.uni-date-picker__container) {
    position: relative;
    z-index: 9999 !important;
  }

  :deep(.uni-date-single--x) {
    z-index: 10000 !important;
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 90vw !important;
    max-width: 400px !important;
    max-height: 80vh !important;
    overflow-y: auto !important;
  }

  :deep(.uni-date-range--x) {
    z-index: 10000 !important;
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 90vw !important;
    max-width: 600px !important;
    max-height: 80vh !important;
    overflow-y: auto !important;
  }

  :deep(.uni-date-mask--pc) {
    z-index: 9999 !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
  }
}

/* 当选择器打开时，防止页面滚动 */
.picker-visible {
  overflow: hidden !important;

  .content {
    overflow: hidden !important;
  }
}</anment_code_snippet>

/* 优化附件上传组件样式 */
:deep(.uni-file-picker) {
  .uni-file-picker__container {
    .file-picker__box {
      .file-picker__box-content {
        padding: 48rpx 24rpx;  // 增加上下内边距
        background-color: #f8f9fc;
        border: 2rpx dashed #e5e7eb;
        border-radius: 16rpx;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 16rpx;
        min-height: 200rpx; // 设置最小高度
        
        &:active {
          background-color: #f5f7fa;
          border-color: #007AFF;
        }

        // 上传图标
        &::before {
          content: '';
          width: 72rpx;
          height: 72rpx;
          background: url('data:image/svg+xml;base64,PHN2ZyB0PSIxNzA0OTY5NjAwMDAwIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjM0NjEiIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIj48cGF0aCBkPSJNNTQ0IDY3MlY0MTZhMzIgMzIgMCAwIDAtNjQgMHYyNTZhMzIgMzIgMCAwIDAgNjQgMHoiIGZpbGw9IiM5OTk5OTkiIHAtaWQ9IjM0NjIiPjwvcGF0aD48cGF0aCBkPSJNNTEyIDc2OGEzMiAzMiAwIDAgMCAzMi0zMlY0ODBhMzIgMzIgMCAwIDAtNjQgMHYyNTZhMzIgMzIgMCAwIDAgMzIgMzJ6IiBmaWxsPSIjOTk5OTk5IiBwLWlkPSIzNDYzIj48L3BhdGg+PHBhdGggZD0iTTY0MCA1NDRIMzg0YTMyIDMyIDAgMCAwIDAgNjRoMjU2YTMyIDMyIDAgMCAwIDAtNjR6IiBmaWxsPSIjOTk5OTk5IiBwLWlkPSIzNDY0Ij48L3BhdGg+PHBhdGggZD0iTTc2OCA1MTJhMzIgMzIgMCAwIDAtMzIgMzJ2MjU2YTMyIDMyIDAgMCAwIDY0IDBWNTQ0YTMyIDMyIDAgMCAwLTMyLTMyek0yNTYgNTEyYTMyIDMyIDAgMCAwLTMyIDMydjI1NmEzMiAzMiAwIDAgMCA2NCAwVjU0NGEzMiAzMiAwIDAgMC0zMi0zMnoiIGZpbGw9IiM5OTk5OTkiIHAtaWQ9IjM0NjUiPjwvcGF0aD48L3N2Zz4=') no-repeat center/contain;
          margin-bottom: 16rpx;
        }

        // 上传文字提示
        &::after {
          content: '点击或拖拽文件到此处上传\A支持图片、文档等格式';  // 添加更多提示信息
          white-space: pre;  // 保留换行符
          text-align: center;
          font-size: 28rpx;
          color: #666;
          font-weight: 500;
          line-height: 1.6;
        }
      }
    }

    // 已上传文件列表样式
    .file-picker__progress {
      margin-top: 24rpx;
      
      .file-picker__progress-item {
        background-color: #f8f9fc;
        border-radius: 12rpx;
        padding: 24rpx;  // 增加内边距
        margin-bottom: 16rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
        
        &:hover {
          background-color: #f5f7fa;
        }
        
        .file-picker__progress-info {
          display: flex;
          align-items: center;
          gap: 16rpx;
          flex: 1;  // 让文件信息占据剩余空间
          min-width: 0;  // 防止文件名过长溢出
          
          .file-picker__progress-name {
            font-size: 28rpx;
            color: #333;
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  // 自定义上传按钮
  .upload-button {
    margin-top: 24rpx;
    
    button {
      width: 100%;
      height: 180rpx;  // 增加按钮高度
      background: #f8f9fc;
      border: 2rpx dashed #e5e7eb;
      border-radius: 16rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 16rpx;
      transition: all 0.3s ease;
      padding: 32rpx 24rpx;  // 增加内边距
      
      &::before {
        content: '';
        width: 64rpx;
        height: 64rpx;
        background: url('data:image/svg+xml;base64,PHN2ZyB0PSIxNzA0OTY5NjAwMDAwIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjM0NjEiIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIj48cGF0aCBkPSJNNTEyIDY0QzI2NC42IDY0IDY0IDI2NC42IDY0IDUxMnMyMDAuNiA0NDggNDQ4IDQ0OCA0NDgtMjAwLjYgNDQ4LTQ0OFM3NTkuNCA2NCA1MTIgNjR6bTAgODIwYy0yMDUuNCAwLTM3Mi0xNjYuNi0zNzItMzcyczE2Ni42LTM3MiAzNzItMzcyIDM3MiAxNjYuNiAzNzIgMzcyLTE2Ni42IDM3Mi0zNzIgMzcyeiIgZmlsbD0iIzk5OTk5OSIgcC1pZD0iMzQ2MiI+PC9wYXRoPjxwYXRoIGQ9Ik01NDQgNjcyVjQxNmEzMiAzMiAwIDAgMC02NCAwdjI1NmEzMiAzMiAwIDAgMCA2NCAweiIgZmlsbD0iIzk5OTk5OSIgcC1pZD0iMzQ2MyI+PC9wYXRoPjxwYXRoIGQ9Ik02NDAgNTQ0SDM4NGEzMiAzMiAwIDAgMCAwIDY0aDI1NmEzMiAzMiAwIDAgMCAwLTY0eiIgZmlsbD0iIzk5OTk5OSIgcC1pZD0iMzQ2NCI+PC9wYXRoPjwvc3ZnPg==') no-repeat center/contain;
      }
      
      &::after {
        content: '点击上传附件\A支持图片、文档等格式';
        white-space: pre;
        text-align: center;
        font-size: 28rpx;
        color: #666;
        font-weight: 500;
        line-height: 1.6;
      }
      
      &:active {
        background-color: #f5f7fa;
        border-color: #007AFF;
        transform: scale(0.98);
      }
    }
  }
}

/* 文件上传项特殊样式 */
.file-upload-item {
  :deep(.uni-forms-item__content) {
    padding: 16rpx 0;
  }
}
</style>
