<template>
  <view class="page-container">
    <!-- 内容区域 -->
    <scroll-view
        :class="['content', { 'picker-visible': isPickerVisible }]"
        scroll-y=""
        @scrolltolower="loadMore"
        :refresher-triggered="isRefreshing"
        @refresherrefresh="onRefresh"
        refresher-enabled="true"
        lower-threshold="50"
    >
      <view v-if="activeTab === 0" class="pwd-retrieve-container">
        <uni-forms ref="form" :model="user" :rules="rules" labelWidth="80px">
          <uni-forms-item name="projectId" label="所属项目" label-width="100px">
            <picker :range="projectNames" @change="onProjectChange" :value="selectedProjectIndex">
              <view class="uni-input">{{ projectNames[selectedProjectIndex] || '请选择所属项目' }}</view>
            </picker>
          </uni-forms-item>

          <uni-forms-item name="startDate" label="加班开始时间" label-width="100px">
            <uni-datetime-picker type="datetime" v-model="user.startDate" @change="onStartDateChange" :min-date="todayTimestamp" :key="startDateKey"/>
          </uni-forms-item>
          <uni-forms-item name="endDate" label="加班结束时间" label-width="100px">
            <uni-datetime-picker type="datetime" v-model="user.endDate" @change="onEndDateChange" :min-date="todayTimestamp" :key="endDateKey"/>
          </uni-forms-item>

          <uni-forms-item name="reason" label="加班理由" label-width="100px">
            <view class="rich-text-editor">
              <uni-easyinput
                  type="textarea"
                  autoHeight
                  v-model="user.reason"
                  placeholder="请输入加班理由"
                  @input="onReasonInput"
                  :maxlength="100"
                  show-word-limit
              />
              <text class="char-count">{{ user.reason.length }}/100</text>
            </view>
          </uni-forms-item>

          <button type="primary" @click="submit">提交</button>
        </uni-forms>
      </view>

      <!-- 查看历史页面 -->
      <view v-else class="history-list">
        <view v-for="(item, index) in workOvertimeHistory" :key="index" class="record-item">
          <view class="record-content">
            <view class="record-main">
              <view class="record-header">
                <view class="user-info">
                  <text class="user-name">{{ item.userName || '未知用户' }}</text>
                  <text class="time-duration">{{ calculateDuration(item.startTime, item.endTime) }}</text>
                </view>
                <view class="record-status" :class="getStatusClass(item)">
                  <text class="debug-info" v-if="false">{{ item.processingStatus	 }}</text>
                  {{ getStatusText(item.processingStatus	) }}
                </view>
              </view>
              
              <view class="record-details">
                <view class="detail-row">
                  <text class="detail-label">所属项目：</text>
                  <text class="detail-value">{{ getProjectNameById(item.projectId) }}</text>
                </view>
                <view class="detail-row">
                  <text class="detail-label">开始时间：</text>
                  <text class="detail-value">{{ formatDateTime(item.startTime) }}</text>
                </view>
                <view class="detail-row">
                  <text class="detail-label">结束时间：</text>
                  <text class="detail-value">{{ formatDateTime(item.endTime) }}</text>
                </view>
                <view class="detail-row reason-row">
                  <text class="detail-label">加班理由：</text>
                  <text class="detail-value reason-text">{{ item.reason || '无' }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="!workOvertimeHistory.length" class="empty-state">
          <text class="empty-text">暂无加班记录</text>
        </view>

        <!-- 加载更多状态 -->
        <uni-load-more :status="loadingMore ? 'loading' : (pageNo >= totalPages ? 'noMore' : 'more')"></uni-load-more>
      </view>
    </scroll-view>

    <!-- TabBar -->
    <view v-if="showTabBar" class="custom-tab-bar">
      <view
          class="tab-item"
          :class="{ active: activeTab === 0 }"
          @click="switchTab(0)"
      >
        <view class="tab-icon">📝</view>
        <text class="tab-text">申请加班</text>
      </view>
      <view
          class="tab-item"
          :class="{ active: activeTab === 1 }"
          @click="switchTab(1)"
      >
        <view class="tab-icon">📋</view>
        <text class="tab-text">查看记录</text>
      </view>
    </view>

  </view>
</template>

<script>

import {getProjects} from "@/api/itsm/project";
import {add, completeTask, list, startWorkFlow} from "@/api/itsm/workOvertime";

export default {
  data() {
    return {
      projectList: [], // 用于存储项目列表
      projectNames: [], // 用于存储项目名称
      selectedProjectIndex: 0, // 当前选中的项目索引
      todayTimestamp: new Date(new Date().setHours(0, 0, 0, 0)).getTime(), // 当天凌晨 00:00:00 的时间戳
      startDateKey: 0,
      endDateKey: 0,

      showTabBar: true, // 控制 TabBar 显示或隐藏
      activeTab: 0, // 标签页索引
      isPickerVisible: false, // 新增状态，用于控制 Picker 是否可见
      isRefreshing: false, // 是否正在刷新
      loadingMore: false, // 是否正在加载更多
      workOvertimeHistory: [],

      pageNo: 1, // 当前页码
      pageSize: 10, // 每页显示条目数
      totalPages: 0, // 总页数

      user: {
        projectId: '',
        reason: '',
        startDate: '',
        endDate: '',
      },
      rules: {
        projectId: {
          rules: [{
            required: true,
            errorMessage: '所属项目不能为空'
          }]
        },
        reason: {
          rules: [{
            required: true,
            errorMessage: '加班理由不能为空'
          }]
        },
        startTime: {
          rules: [{
            required: true,
            errorMessage: '开始时间不能为空'
          }]
        },
        endTime: {
          rules: [{
            required: true,
            errorMessage: '结束时间不能为空'
          }]
        }
      },
    }
  },
  mounted() {
    this.$refs.form.setRules(this.rules)
    this.fetchProjects(); // 在组件加载时请求项目列表
  },

  watch: {
    projectList(newList) {
      if (newList.length > 0) {
        this.projectNames = newList.map(project => project.projectName);
        this.projectNames.unshift('请选择所属项目'); // 在列表开头添加默认选项

        // 构建 projectId 到项目名称的映射表
        this.projectMap = Object.fromEntries(
            newList.map((project) => [project.projectId, project.projectName])
        );
      }
    }
  },

  methods: {

    switchTab(tabIndex) {
      this.activeTab = tabIndex;
      if (tabIndex === 1) {
        this.pageNo = 1;
        this.fetchWorkOvertimeHistory().catch(error => {
          uni.showToast({
            title: '加载失败',
            icon: 'none'
          });
        });
      }
    },
    // 下拉刷新
    async onRefresh() {
      if (this.isRefreshing) return;
      this.isRefreshing = true;
      
      try {
        this.pageNo = 1;
        await this.fetchWorkOvertimeHistory();
      } catch (error) {
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.isRefreshing = false;
        uni.stopPullDownRefresh();
      }
    },

    // 上拉加载更多
    async loadMore() {
      if (this.loadingMore || this.pageNo >= this.totalPages) return;
      this.loadingMore = true;
      
      try {
        this.pageNo++;
        await this.fetchWorkOvertimeHistory(true);
      } finally {
        this.loadingMore = false;
      }
    },

    // 获取加班历史记录
    async fetchWorkOvertimeHistory(isLoadMore = false) {
      const queryParams = {
        pageNum: this.pageNo,
        pageSize: this.pageSize
      };

      try {
        const response = await list(queryParams);
        console.log('加班记录原始数据:', response); // 添加日志

        if (response.code === 200) {
          const { rows, total } = response;
          if (isLoadMore) {
            this.workOvertimeHistory = [...this.workOvertimeHistory, ...rows];
          } else {
            this.workOvertimeHistory = rows;
          }
          console.log('处理后的加班记录:', this.workOvertimeHistory); // 添加日志
          console.log('当前状态样式:', this.getStatusClass(this.workOvertimeHistory[0])); // 检查状态样式
          this.totalPages = Math.ceil(total / this.pageSize);
        }
      } catch (error) {
        console.error('获取加班记录失败:', error);
        throw error;
      }
    },

    getProjectNameById(projectId) {
      return this.projectMap[projectId] || "未知项目";
    },

    fetchProjects() {
      getProjects({}).then(response => {
        console.log('加载项目列表结果：' + JSON.stringify(response,null,6))
        if (response.code === 200) {
          this.projectList = response.rows;
          this.projectNames = this.projectList.map(project => project.projectName);
          this.projectNames.unshift('请选择所属项目'); // 在列表开头添加默认选项
        } else {
          uni.showToast({ "title": "获取项目列表失败", "icon": 'none' });
        }
      }).catch(err => {
        uni.showToast({ "title": "获取项目列表失败", "icon": 'none' });
      });
    },

    onProjectChange(e) {
      console.log('项目选中发生了变化：' + e.detail.value)
      console.log('this.projectList输出一下：' + JSON.stringify(this.projectList,null,6))

      const selectedIndex = e.detail.value; // 获取用户选择的索引
      this.selectedProjectIndex = selectedIndex;

      if (selectedIndex > 0) { // 跳过 "请选择所属项目" 选项
        this.user.projectId = this.projectList[selectedIndex - 1].projectId; // 选中的项目对应的 projectId
      } else {
        this.user.projectId = ''; // 重置为未选择状态
      }
    },

    onStartDateChange(e) {
      this.user.startDate = e
      this.validateDateRange(); // 校验开始时间和结束时间
    },

    onEndDateChange(e) {
      this.user.endDate = e
      this.validateDateRange(); // 校验开始时间和结束时间
    },

    onReasonInput(e) {
      // 实时更新用户输入的加班理由
      const inputText = e;
      if (inputText.length <= 100) {
        this.user.reason = inputText;
      } else {
        uni.showToast({
          "title": '加班理由限定100字',
          "icon": 'none'
        });
        // 回滚到之前的值
        this.user.reason = this.user.reason.slice(0, 100);
      }
    },

    validateDateRange() {
      if (this.user.startDate && this.user.endDate) {
        const startTime = new Date(this.user.startDate).getTime();
        const endTime = new Date(this.user.endDate).getTime();

        if (startTime < this.todayTimestamp) {
          uni.showToast({
            title: "开始时间不能早于当天",
            icon: "none"
          });
          this.user.startDate = '';
          this.startDateKey++; // 更新 key 强制刷新
          this.$forceUpdate(); // 强制更新
        }

        if (endTime < this.todayTimestamp) {
          this.user.startDate = '';
          uni.showToast({
            title: "结束时间不能早于当天",
            icon: "none"
          });
          this.$forceUpdate(); // 强制更新
        }

        if (startTime > endTime) {
          uni.showToast({
            title: "开始时间不能大于结束时间",
            icon: "none"
          });
          this.user.endDate = '';
          this.endDateKey++; // 更新 key 强制刷新
        }
      }
    },

    submit() {
      this.$refs.form.validate().then(() => {
        const leaveRequest = {
          projectId: this.user.projectId,
          startTime: this.user.startDate,
          endTime: this.user.endDate,
          reason: this.user.reason,
          processingStatus: 'draft'
        };

        add(leaveRequest).then(res => {
          if (res.code === 200) {
            const submitFormData = {
              businessKey: res.data.overtimeId,
              tableName: 'itsm_attendance_work_overtime_log',
              variables: {
                entity: res.data,
                userList: [1, 3],
                userList2: [1, 3]
              }
            };

            startWorkFlow(submitFormData).then(workflowRes => {
              if (workflowRes.code === 200) {
                const taskForm = {
                  taskId: workflowRes.data.taskId,
                  taskVariables: {},
                  messageType: ['1'],
                  wfCopyList: []
                };

                completeTask(taskForm).then(taskRes => {
                  console.log('app端提交加班申请，工作流提交结果：' + JSON.stringify(taskRes, null, 6));

                  // 所有异步操作完成后才显示成功消息并返回
                  uni.showToast({ title: "提交成功", icon: 'success', duration: 2000 }); // 设置持续时间以确保提示可见

                  // 在成功消息显示一定时间后返回上一页
                  setTimeout(() => {
                    uni.navigateBack(); // 返回上一页
                  }, 2000); // 等待2秒再返回，确保提示框显示完毕
                }).catch(err => {
                  console.error('完成任务失败', err);
                  uni.showToast({ title: "提交失败", icon: 'none' });
                });
              } else {
                console.error('启动工作流失败', workflowRes);
                uni.showToast({ title: "提交失败", icon: 'none' });
              }
            }).catch(err => {
              console.error('启动工作流失败', err);
              uni.showToast({ title: "提交失败", icon: 'none' });
            });
          } else {
            console.error('添加请求失败', res);
            uni.showToast({ title: "提交失败", icon: 'none' });
          }

          // 清空表单数据应该放在所有异步操作完成之后
          if (res.code === 200) {
            this.user = {
              projectId: '',
              reason: '',
              startDate: '',
              endDate: ''
            };
          }
        }).catch(err => {
          console.error('添加请求失败', err);
          uni.showToast({ title: "提交失败", icon: 'none' });
        });
      }).catch(err => {
        console.log('表单校验失败', err);
      });
    },

    // 格式化日期显示
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
    },

    // 格式化日期时间显示
    formatDateTime(dateString) {
      if (!dateString) return '未设置';
      try {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (e) {
        return dateString;
      }
    },

    // 计算时长
    calculateDuration(startTime, endTime) {
      if (!startTime || !endTime) return '未知时长';
      try {
        const start = new Date(startTime);
        const end = new Date(endTime);
        const hours = Math.round((end - start) / (1000 * 60 * 60));
        return `${hours}小时`;
      } catch (e) {
        return '计算错误';
      }
    },

    // 获取状态样式类
    getStatusClass(item) {
      if (!item || !item.processingStatus) return 'status-draft';
      const status = item.processingStatus;
      return `status-${status}`;
    },

    // 获取状态文本
    getStatusText(status) {
      if (!status) return '未知状态';
      const statusMap = {
        'cancel': '已撤销',
        'draft': '草稿',
        'waiting': '待审核',
        'finish': '已完成',
        'invalid': '已作废',
        'back': '已回退',
        'termination': '已终止',
        'inReview': '审核中'
      };
      return statusMap[status] || '未知状态';
    },
  }
}
</script>

<style lang="scss">
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: calc(80rpx + env(safe-area-inset-bottom)); // 调整为与tabbar相同的高度
}

/* 记录列表样式优化 */
.history-list {
  padding: 20rpx 24rpx;
  background-color: #f7f8fa;
}

.record-item {
  background-color: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.03);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
  }
}

.record-content {
  .record-date {
    padding: 20rpx 24rpx 0;
    font-size: 24rpx;
    color: #999;
  }

  .record-main {
    padding: 24rpx;

    .record-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;
      padding-bottom: 16rpx;
      border-bottom: 2rpx solid #f5f7fa;
      
      .user-info {
        display: flex;
        align-items: center;
        gap: 16rpx;
        
        .user-name {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
        }
        
        .time-duration {
          font-size: 24rpx;
          color: #666;
          background-color: #f8f9fc;
          padding: 4rpx 16rpx;
          border-radius: 24rpx;
        }
      }
      
      .record-status {
        font-size: 24rpx;
        padding: 6rpx 20rpx;
        border-radius: 24rpx;
        font-weight: 500;
        
        // 已完成
        &.status-finish {
          background-color: rgba(82, 196, 26, 0.08) !important;
          color: #52c41a !important;
        }
        
        // 待审核
        &.status-waiting {
          background-color: rgba(24, 144, 255, 0.08) !important;
          color: #1890ff !important;
        }
        
        // 草稿
        &.status-draft {
          background-color: rgba(191, 191, 191, 0.08) !important;
          color: #8c8c8c !important;
        }
        
        // 已作废
        &.status-invalid {
          background-color: rgba(245, 34, 45, 0.08) !important;
          color: #f5222d !important;
        }
        
        // 已撤销
        &.status-cancel {
          background-color: rgba(250, 173, 20, 0.08) !important;
          color: #faad14 !important;
        }
        
        // 已回退
        &.status-back {
          background-color: rgba(114, 46, 209, 0.08) !important;
          color: #722ed1 !important;
        }
        
        // 已终止
        &.status-termination {
          background-color: rgba(207, 19, 34, 0.08) !important;
          color: #cf1322 !important;
        }
        
        // 审核中
        &.status-inReview {
          background-color: rgba(250, 173, 20, 0.08) !important;
          color: #faad14 !important;
        }
      }
    }

    .record-details {
      background-color: #fafbfc;
      border-radius: 16rpx;
      padding: 20rpx;
      
      .detail-row {
        display: flex;
        margin-bottom: 16rpx;
        align-items: flex-start;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        &.reason-row {
          margin-top: 16rpx;
          padding-top: 16rpx;
          border-top: 2rpx solid rgba(0,0,0,0.03);
        }

        .detail-label {
          width: 140rpx;
          color: #666;
          font-size: 26rpx;
          flex-shrink: 0;
          padding-top: 2rpx;
        }

        .detail-value {
          flex: 1;
          color: #333;
          font-size: 26rpx;
          line-height: 1.5;
          
          &.reason-text {
            word-break: break-all;
            white-space: pre-wrap;
          }
        }
      }
    }
  }
}

/* 空状态样式优化 */
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
  
  .empty-text {
    color: #999;
    font-size: 28rpx;
    background-color: #f8f9fc;
    padding: 16rpx 32rpx;
    border-radius: 32rpx;
  }
}

/* 加载更多样式优化 */
.uni-load-more {
  padding: 32rpx 0;
  
  .uni-load-more__text {
    color: #999;
    font-size: 24rpx;
  }
}

/* 自定义TabBar样式优化 */
.custom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: rgba(255, 255, 255, 0.98);
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -1rpx 6rpx rgba(0,0,0,0.03);
  backdrop-filter: blur(10px);
  z-index: 100;
  padding-bottom: env(safe-area-inset-bottom);

  .tab-item {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    padding: 4rpx 0;
    transition: all 0.3s ease;
    
    &.active {
      .tab-icon {
        transform: scale(1.05);
      }
      
      .tab-text {
        color: #007AFF;
        font-weight: 500;
      }
      
      &::after {
        content: '';
        position: absolute;
        bottom: 12rpx;
        width: 24rpx;
        height: 3rpx;
        background-color: #007AFF;
        border-radius: 2rpx;
        opacity: 0.8;
      }
    }

    .tab-icon {
      font-size: 36rpx;
      height: 36rpx;
      line-height: 36rpx;
      margin-bottom: 6rpx;
      transition: transform 0.2s ease;
    }

    .tab-text {
      font-size: 24rpx;
      color: #666;
      line-height: 1;
    }
  }
}

/* 表单容器样式 */
.pwd-retrieve-container {
  background-color: #fff;
  padding: 30rpx;
  margin: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.05);
}

/* 输入框样式 */
.uni-input {
  padding: 20rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  width: 100%;
  box-sizing: border-box;
  font-size: 28rpx;
  background-color: #f9f9f9;
}

/* 富文本编辑器样式 */
.rich-text-editor {
  position: relative;
  margin-bottom: 30rpx;

  .char-count {
    position: absolute;
    bottom: -30rpx;
    right: 0;
    color: #999;
    font-size: 24rpx;
  }
}

/* 加载更多样式 */
.uni-load-more {
  padding: 20rpx 0;
}

/* 内容区域样式优化 */
.content {
  flex: 1;
  overflow-y: auto;
  background-color: #f7f8fa;
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
  
  &.refreshing {
    transition: transform 0.3s ease;
  }
}

</style>

