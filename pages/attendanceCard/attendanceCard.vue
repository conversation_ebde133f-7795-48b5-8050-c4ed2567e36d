<template>
  <view class="page-container">
    <!-- 添加一个隐藏的地图容器 -->
    <view id="container" style="display: none; height: 1px;"></view>

    <!--顶部个人信息栏-->
    <view class="header-section">
      <view class="user-info-container">
        <view class="user-avatar-section">
          <view v-if="!avatar" class="cu-avatar xl round">
            <view class="iconfont icon-people text-gray"></view>
          </view>
          <image v-if="avatar" :src="avatar" @click="handleToLogout" class="cu-avatar xl round"
                 mode="aspectFill"></image>
        </view>
        <view class="user-details">
          <view v-if="!name" @click="handleToLogin" class="login-tip">
            点击登录
          </view>
          <view v-if="name" class="user-name">
            {{ name }}
          </view>
          <view class="department-info">中山市统一运维平台</view>
        </view>
        <view @click="handleToInfo" class="profile-link">
          <text>个人信息</text>
          <uni-icons type="right" size="16" color="#fff"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 考勤打卡区域 -->
    <view class="attendance-section" :class="{ 'non-working-hours': !isInWorkingHours }">
      <!-- 非考勤时间提示 -->
      <view v-if="!isInWorkingHours" class="non-working-hours-tip">
        <uni-icons type="info" size="24" color="#999"></uni-icons>
        <text>当前时间不在考勤时间范围内</text>
      </view>

      <!-- 班次信息展示 -->
      <uni-section title="考勤打卡" type="line">
        <uni-card :is-shadow="false" :border="false" style="margin: 0px; padding: 0px;">
          <view class="shift-info-card">
            <template v-if="isDataLoaded && shiftInfo.shiftName">
              <view class="shift-header">
                <view class="shift-title">
                  <text class="shift-name">{{ shiftInfo.shiftName }}</text>
                  <text class="attendance-status" :class="{ 'status-active': isInWorkingHours }">
                    {{ attendanceStatusText }}
                  </text>
                </view>
                <text class="working-hours">{{ shiftInfo.workingHours }}</text>
              </view>

              <view class="shift-details">
                <view class="info-row">
                  <text class="label">考勤组：</text>
                  <text class="value">{{ shiftInfo.groupName }}</text>
                </view>

                <view class="attendance-time-info">
                  <view class="time-section">
                    <view class="section-title">排班时间</view>
                    <view class="time-range">
                      <text>{{ shiftInfo.startTime || '--:--' }}</text>
                      <text class="separator">-</text>
                      <text>{{ shiftInfo.endTime || '--:--' }}</text>
                    </view>
                  </view>

                  <view class="time-section">
                    <view class="section-title">班次打卡时间</view>
                    <view class="punch-times">
                      <view class="punch-time">
                        <text class="time-label">上班打卡时间</text>
                        <text class="time-value">{{ shiftInfo.punchInTime || '--:--' }}</text>
                      </view>
                      <view class="punch-time">
                        <text class="time-label">下班打卡时间</text>
                        <text class="time-value">{{ shiftInfo.punchOutTime || '--:--' }}</text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </template>
            <template v-else>
              <view class="no-schedule">
                <uni-icons type="info" size="24" color="#999"></uni-icons>
                <text>暂无排班</text>
              </view>
            </template>
          </view>

          <!-- 打卡操作区域 -->
          <view class="clock-actions" :class="{ 'disabled-actions': !isInWorkingHours }">
            <uni-row>
              <!-- 上班打卡 -->
              <uni-col :span="12">
                <view class="clock-card"
                      :class="[
                        { active: clockType === 'clockIn' },
                        { disabled: !isDataLoaded || !shiftInfo.shiftName || isAfterNoon() }
                      ]"
                      @click="selectClockType('clockIn')">
                  <view class="card-content">
                    <text class="card-title">上班打卡</text>
                    <text class="card-status" :class="{ 'status-done': attendanceRecord.clockInTime }">
                      {{ getClockInStatusText() }}
                    </text>
                  </view>
                </view>
              </uni-col>

              <!-- 下班打卡 -->
              <uni-col :span="12">
                <view class="clock-card"
                      :class="[
                        { active: clockType === 'clockOut' },
                        { disabled: !isDataLoaded || !shiftInfo.shiftName || !isInWorkingHours || attendanceRecord.clockOutTime || !canClockOut() }
                      ]"
                      @click="selectClockType('clockOut')">
                  <view class="card-content">
                    <text class="card-title">下班打卡</text>
                    <text class="card-status" :class="{ 'status-done': attendanceRecord.clockOutTime }">
                      {{ getClockOutStatusText() }}
                    </text>
                  </view>
                </view>
              </uni-col>
            </uni-row>
          </view>

          <!-- 打卡按钮 -->
          <view class="clock-button-section">
            <button class="clock-button"
                    @click="onClockAction()"
                    :disabled="isClockButtonDisabled() || isClockOutDisabled"
                    :class="{ 'button-disabled': isClockButtonDisabled() || isClockOutDisabled }">
              <text class="button-text">{{ getButtonText() }}</text>
            </button>
            <view class="records-link" @click="viewClockInRecords">
              <text>查看打卡记录</text>
              <uni-icons type="right" size="14" color="#007AFF"></uni-icons>
            </view>
          </view>
        </uni-card>
      </uni-section>
    </view>
  </view>
</template>

<script>
import {
  getUserRecordInfo,
  getItsmAttendanceSchedulingInfo,
  add,
  update
} from '@/api/itsm/attendance';

export default {
  data() {
    return {
      name: this.$store.state.user.name,
      clockType: "clockIn", // 默认选中上班打卡
      currentTime: '',

      clockInStatus: "未打卡", // 上班打卡状态
      clockOutStatus: "未打卡", // 下班打卡状态
      location: null, // 用户位置信息
      isDataLoaded: false, // 控制数据是否加载成功

      // 添加模拟的班次数据
      shiftInfo: {
        shiftName: '',
        startTime: '', // 考勤开始时间
        endTime: '', // 考勤结束时间
        punchInTime: '', // 上班打卡时间
        punchOutTime: '', // 下班打卡时间
        groupName: '',
        workingHours: '',
        schedulingId: ''
      },

      attendanceRecord: {}, // 存储用户打卡记录
      isClockOutDisabled: false, // 新增状态管理
      isClockInExpired: false, // 新增：上班打卡是否过期标志
    };
  },

  computed: {
    avatar() {
      return this.$store.state.user.avatar
    },

    windowHeight() {
      return uni.getSystemInfoSync().windowHeight - 50
    },
    // 判断是否迟到
    isLate() {
      if (!this.currentTime || !this.shiftInfo.punchInTime) return false;
      return this.currentTime > this.shiftInfo.punchInTime;
    },

    // 判断是否
    isEarly() {
      if (!this.currentTime || !this.shiftInfo.punchOutTime) return false;
      return this.currentTime < this.shiftInfo.punchOutTime;
    },

    // 判断当前时间是否在考勤时间范围
    isInWorkingHours() {
      if (!this.shiftInfo.startTime || !this.shiftInfo.endTime) return false;

      const now = new Date();
      const currentDate = now.toISOString().split('T')[0]; // 获取当前日期格式为 yyyy-MM-dd

      // 获取排班开始和结束日期
      const startDate = this.shiftInfo.startTime; // 假设 startTime 是 yyyy-MM-dd
      const endDate = this.shiftInfo.endTime; // 假设 endTime 是 yyyy-MM-dd

      // 判断当前日期是否排班日期范围内
      if (currentDate < startDate || currentDate > endDate) {
        return false; // 当前日期不在排班范围内
      }

      // 如果在排班日期范围内，返回 true
      return true;
    },

    // 修改考勤状态文
    attendanceStatusText() {
      if (!this.isDataLoaded || !this.shiftInfo.shiftName) return '无需打卡';
      if (!this.isInWorkingHours) return '非考勤时间';
      return '考勤中';
    }
  },

  methods: {
    handleToLogout() {
      this.$tab.navigateTo('/pages/mine/logout/index')
    },

    handleToInfo() {
      this.$tab.navigateTo('/pages/mine/info/index')
    },

    viewClockInRecords() {
      this.$tab.navigateTo('/pages/attendanceCard/record/index')
    },

    // 计算工作时长
    calculateWorkingHours(punchInTime, punchOutTime) {
      // 如果没有打卡时间，返回0小时
      if (!punchInTime || !punchOutTime) {
        return '0小时';
      }

      try {
        // 确保时间格式正确（HH:mm:ss）
        const timeRegex = /^\d{2}:\d{2}:\d{2}$/;
        if (!timeRegex.test(punchInTime) || !timeRegex.test(punchOutTime)) {
          console.error('打卡时间格式错误:', {punchInTime, punchOutTime});
          return '0小时';
        }

        // 解析时间
        const [startHour, startMinute] = punchInTime.split(':').map(Number);
        const [endHour, endMinute] = punchOutTime.split(':').map(Number);

        // 计算总分钟数
        let totalMinutes = (endHour * 60 + endMinute) - (startHour * 60 + startMinute);

        // 处理天情
        if (totalMinutes < 0) {
          totalMinutes += 24 * 60;
        }

        // 减去休息时间（1.5小时 = 90分钟）
        totalMinutes -= 90;

        // 确保总分钟数不为负
        if (totalMinutes < 0) {
          totalMinutes = 0;
        }

        // 转换为小时和分钟
        const hours = Math.floor(totalMinutes / 60);
        const minutes = totalMinutes % 60;

        // 返回格式化的工作时长
        return hours > 0
            ? `${hours}小时${minutes > 0 ? `${minutes}分钟` : ''}`
            : (minutes > 0 ? `${minutes}分钟` : '0小时');
      } catch (e) {
        console.error('计算工作时长错误:', e, {punchInTime, punchOutTime});
        return '0小时';
      }
    },

    // 获取班次信息
    getShiftInfo() {
      getItsmAttendanceSchedulingInfo().then(res => {
        console.log('页面加载完成，获取到的班次数据：', JSON.stringify(res.data, null, 6));

        if (res.code === 200) {
          // 如果返回的数据为 null，说明当天不需要考勤
          if (!res.data) {
            this.isDataLoaded = true;
            this.shiftInfo = {
              shiftName: '',
              startTime: '',
              endTime: '',
              punchInTime: '',
              punchOutTime: '',
              groupName: '',
              workingHours: '',
              schedulingId: ''
            };
            return;
          }

          // 有排班数据时的处理逻辑
          const {
            shiftName,
            startTime,
            endTime,
            punchInTime,
            punchOutTime,
            groupName
          } = res.data;

          // 计算工作时长
          const workingHours = this.calculateWorkingHours(punchInTime, punchOutTime);

          // 更新班次信息
          this.shiftInfo = {
            shiftName,
            startTime,
            endTime,
            punchInTime,
            punchOutTime,
            groupName,
            workingHours
          };

          this.isDataLoaded = true;
        } else {
          this.isDataLoaded = false;
          uni.showToast({
            title: "获取排班信息失败",
            icon: 'none'
          });
        }
      }).catch(err => {
        // 只有在网络请求真正失败时才处理错误
        this.isDataLoaded = false;
        console.error('获取班次息失败:', err);
        uni.showToast({
          title: "获取排班信息失败",
          icon: 'none'
        });
      });
    },

    // 获取用户考勤数据
    getItsmAttendanceSchedulingInfo() {
      getUserRecordInfo().then(res => {
        console.log('页面加载完成，获取到的用户的打卡数据：' + JSON.stringify(res.data, null, 6));

        if (res.code === 200) {
          // 初始化考勤记录
          this.attendanceRecord = res.data || {};
          this.updateClockStatus();

          // 检查是否超过中午12点
          this.isClockInExpired = this.isAfterNoon();
        } else {
          this.attendanceRecord = {};
          console.error('获取打卡记录失败：', res.msg);
        }
      }).catch(err => {
        this.attendanceRecord = {};
        console.error('获取打卡记录失败：', err);
      });
    },

    // 更新打卡状态
    updateClockStatus() {
      // 更新上班打卡状态
      if (this.attendanceRecord.clockInTime) {
        const scheduledTime = this.shiftInfo.punchInTime;
        const actualTime = this.attendanceRecord.clockInTime;

        if (this.compareTime(actualTime, scheduledTime) > 0) {
          this.clockInStatus = `已打卡 ${actualTime} (迟到)`;
        } else {
          this.clockInStatus = `已打卡 ${actualTime} (正常)`;
        }
        this.isClockOutDisabled = false;
      } else {
        this.clockInStatus = '未打卡';
      }

      // 更新下班打卡状态
      if (this.attendanceRecord.clockOutTime) {
        const scheduledTime = this.shiftInfo.punchOutTime;
        const actualTime = this.attendanceRecord.clockOutTime;

        if (this.compareTime(actualTime, scheduledTime) < 0) {
          this.clockOutStatus = `已打卡 ${actualTime} (早退)`;
        } else {
          this.clockOutStatus = `已打卡 ${actualTime} (正常)`;
        }
        this.isClockOutDisabled = true;
      } else {
        this.clockOutStatus = '未打卡';
      }
    },

    updateTime() {
      const now = new Date();
      const hours = String(now.getHours()).padStart(2, '0'); // 确保小时数为两位数
      const minutes = String(now.getMinutes()).padStart(2, '0'); // 确保分钟数为两位数
      this.currentTime = `${hours}:${minutes}`; // 格式化时间为HH:mm
    },

    // 修改切换打卡类型的逻辑
    selectClockType(type) {
      // 基础条件检查
      if (!this.isDataLoaded || !this.shiftInfo.shiftName) return;

      // 非考勤时间不允许切换
      if (!this.isInWorkingHours) {
        uni.showToast({
          title: '非考勤时间',
          icon: 'none'
        });
        return;
      }

      // 如果已有下班打卡时间，不允许切换
      if (this.attendanceRecord.clockOutTime) {
        uni.showToast({
          title: '今日打卡已完成',
          icon: 'none'
        });
        return;
      }

      // 如果是上班打卡且已过12点，不允许切换
      if (type === 'clockIn' && this.isAfterNoon()) {
        uni.showToast({
          title: '上班打卡已过期',
          icon: 'none'
        });
        return;
      }

      // 如果是下班打卡且未到12点，不允许切换
      if (type === 'clockOut' && !this.isAfterNoon()) {
        uni.showToast({
          title: '未到下班打卡时间',
          icon: 'none'
        });
        return;
      }

      // 允许切换类型
      this.clockType = type;
    },

    // 修改打卡按钮是否禁用的逻辑
    isClockButtonDisabled() {
      // 基础条件无数据或无排班时禁用
      if (!this.isDataLoaded || !this.shiftInfo.shiftName) return true;

      // 非考勤时间禁用
      if (!this.isInWorkingHours) return true;

      // 如果下班打卡时间已存在，禁用按钮
      if (this.attendanceRecord.clockOutTime) {
        return true;
      }

      // 如果是上班打卡且已过12点，禁用按钮
      if (this.clockType === 'clockIn' && this.isAfterNoon()) {
        return true;
      }

      // 如果是下班打卡且未到12点，禁用按钮
      if (this.clockType === 'clockOut' && !this.isAfterNoon()) {
        return true;
      }

      return false;
    },

    // 修改按钮文本显示逻辑
    getButtonText() {
      if (!this.isDataLoaded || !this.shiftInfo.shiftName) {
        return '无需打卡';
      }

      if (!this.isInWorkingHours) {
        return '非考勤时间';
      }

      // 如已有下班打卡时间，显示今日打卡已完成
      if (this.attendanceRecord.clockOutTime) {
        return '今日打卡已完成';
      }

      // 如果是上班打卡且已过12点
      if (this.clockType === 'clockIn' && this.isAfterNoon()) {
        return '上班打卡已过期';
      }

      // 如果是下班打卡
      if (this.clockType === 'clockOut') {
        if (!this.isAfterNoon()) {
          return '未到下班打卡时间';
        }
        return '下班打卡';
      }

      return '上班打卡';
    },

    // 新增：获取位置信息方法
    async getLocationInfo() {
      return new Promise((resolve, reject) => {
        // 检查浏览器是否支持地理定位
        if (!navigator.geolocation) {
          uni.showToast({
            title: '您的浏览器不支持地理定位',
            icon: 'none'
          });
          reject(new Error('浏览器不支持地理定位'));
          return;
        }

        // 配置定位选项
        const options = {
          enableHighAccuracy: true, // 启用高精度定位
          timeout: 10000,           // 10秒超时
          maximumAge: 0             // 不使用缓存定位
        };

        // 尝试获取位置信息
        navigator.geolocation.getCurrentPosition(
            (position) => {
              console.log('获取位置信息成功：', position);
              resolve({
                latitude: position.coords.latitude,
                longitude: position.coords.longitude,
                accuracy: position.coords.accuracy
              });
            },
            (error) => {
              console.error('获取位置信息失败：', error);

              // 详细错误处理
              switch (error.code) {
                case error.PERMISSION_DENIED:
                  uni.showModal({
                    title: '定位权限',
                    content: '需要位置权限才能打卡，请在浏览器设置中允许位置访问',
                    confirmText: '去设置',
                    success: (res) => {
                      if (res.confirm) {
                        // 引导用户打开浏览器设置
                        window.open('chrome://settings/content/location', '_blank');
                      }
                    }
                  });
                  break;
                case error.POSITION_UNAVAILABLE:
                  uni.showToast({
                    title: '位置信息不可用，请检查网络和GPS',
                    icon: 'none'
                  });
                  break;
                case error.TIMEOUT:
                  uni.showToast({
                    title: '获取位置超时，请稍后重试',
                    icon: 'none'
                  });
                  break;
                default:
                  uni.showToast({
                    title: '未知定位错误',
                    icon: 'none'
                  });
              }

              reject(error);
            },
            options
        );
      });
    },

    // 新增备选定位方法
    async getAlternativeLocation() {
      try {
        // 使用IP定位为备选方案
        const response = await uni.request({
          url: 'https://ipapi.co/json/', // 可靠的IP定位服务
          method: 'GET'
        });

        const data = response[1].data;

        if (data && data.latitude && data.longitude) {
          uni.showToast({
            title: '使用IP定位成功',
            icon: 'none'
          });

          return {
            latitude: data.latitude,
            longitude: data.longitude,
            type: 'IP_LOCATION'
          };
        }
      } catch (error) {
        console.error('IP定位失败：', error);
      }

      throw new Error('无法获取位置信息');
    },

    // 新增高德地图定位方法
    async getAMapLocation() {
      return new Promise((resolve, reject) => {
        console.log('开始高德地图定位');

        if (!window.AMap) {
          console.error('高德地图SDK未加载');
          reject(new Error('高德地图SDK未加载'));
          return;
        }

        // 创建地图实例
        const map = new window.AMap.Map('container', {
          zoom: 11,
          center: [113.3642, 22.5760] // 添加中山市的大致坐标作为中心点
        });

        window.AMap.plugin(['AMap.Geolocation', 'AMap.Geocoder'], () => {
          // 创建定位实例
          const geolocation = new window.AMap.Geolocation({
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 0,
            convert: true, // 自动偏移坐标为高德坐标
            showButton: false,
            zoomToAccuracy: true, // 定位成功后调整地图视野范围
            noIpLocate: 0,  // 允许IP定位
            noGeoLocation: 0, // 允许浏览器定位
            GeoLocationFirst: true, // 优先使用浏览器定位
            useNative: true // 使用系统定位
          });

          // 创建地理编码实例
          const geocoder = new window.AMap.Geocoder({
            city: "中山", // 指定默认城市
            radius: 1000,
            extensions: "all"
          });

          // 先将定位实例绑定到地图上
          map.addControl(geolocation);

          // 开始定位
          geolocation.getCurrentPosition(async (status, result) => {
            console.log('定位状态:', status);
            console.log('定位结果:', JSON.stringify(result, null, 2));

            if (status === 'complete') {
              const {lng, lat} = result.position;

              try {
                const address = await new Promise((resolveGeo, rejectGeo) => {
                  geocoder.getAddress([lng, lat], (geoStatus, geoResult) => {
                    console.log('地理编码状态:', geoStatus);
                    console.log('原始地理编码结果:', JSON.stringify(geoResult, null, 2));

                    if (geoStatus === 'complete' && geoResult.regeocode) {
                      resolveGeo(geoResult.regeocode);
                    } else {
                      // 增加更详细的错误信息
                      const errorMsg = geoResult?.info || '未知错误';
                      console.error('地理编码失败，错误信息:', errorMsg);
                      rejectGeo(new Error(`地理编码失败: ${errorMsg}`));
                    }
                  });
                });

                // 确保地址组件存在
                const addressComponent = address.addressComponent || {};
                const streetNumber = addressComponent.streetNumber || {};

                // 构建位置信息对象
                const locationInfo = {
                  latitude: lat,
                  longitude: lng,
                  accuracy: result.accuracy || 0,
                  type: 'AMAP_LOCATION',
                  formattedAddress: address.formattedAddress || result.formattedAddress || '未知地址',
                  addressComponent: {
                    province: addressComponent.province || result.addressComponent?.province || '广东省',
                    city: addressComponent.city || result.addressComponent?.city || '中山市',
                    district: addressComponent.district || result.addressComponent?.district || '',
                    street: streetNumber.street || result.addressComponent?.street || '',
                    number: streetNumber.number || result.addressComponent?.number || ''
                  },
                  // 添加原始定位信息
                  location: {
                    type: result.location_type,
                    message: result.message,
                    accuracy: result.accuracy,
                    isConverted: result.isConverted,
                    timestamp: result.timestamp
                  }
                };

                // 输出详细日志
                console.group('📍 详细位置信息');
                console.log('原始定位结果:', result);
                console.log('地理编码结果:', address);
                console.log('最终位置信息:', locationInfo);
                console.groupEnd();

                // 显示成功提示
                uni.showToast({
                  title: `定位成功：${locationInfo.formattedAddress}`,
                  icon: 'none',
                  duration: 3000
                });

                resolve(locationInfo);
              } catch (error) {
                console.error('地理编码过程出错:', error);
                // 使用定位结果中的地址信息作为后备方案
                const fallbackLocationInfo = {
                  latitude: lat,
                  longitude: lng,
                  accuracy: result.accuracy || 0,
                  type: 'AMAP_LOCATION_FALLBACK',
                  formattedAddress: result.formattedAddress || '未知地址',
                  addressComponent: {
                    province: result.addressComponent?.province || '广东省',
                    city: result.addressComponent?.city || '中山市',
                    district: result.addressComponent?.district || '',
                    street: result.addressComponent?.street || '',
                    number: result.addressComponent?.number || ''
                  }
                };

                // 使用后备方案而不是直接报错
                console.log('使用定位结果作为后备方案:', fallbackLocationInfo);
                resolve(fallbackLocationInfo);
              }
            } else {
              console.error('定位失败:', result);
              reject(new Error(result.message || '定位失败'));
            }
          });
        });
      });
    },

    //uniapp默认的调用获取经纬度的方式
    async getUniAppLocation() {
      return new Promise((resolve, reject) => {
        console.log("正在获取高德地图当前定位 ")
        uni.getLocation({
          type: 'gcj02',
          success: (res) => {
            console.log("高德地图获取到的经纬度信息 => ", JSON.stringify(res,null,6))
            resolve(res)
          },
          fail: (err) => {
            console.error("高德地图获取当前定位失败 => ", JSON.stringify(err,null,6))
            // 使用默认坐标,用户在排班信息设置的经纬度
            const location = {
              latitude: undefined,
              longitude: undefined,
              type: 'DEFAULT'
            };
            // 使用后备方案而不是直接报错
            console.log('使用定位结果作为后备方案:', location);
            resolve(location);
            // reject(new Error('定位失败'));
          }
        })
      });
    },

    // 修改打卡方法，使用高德地图定位
    async onClockAction() {
      try {
        // 检查是否可以打卡
        if (this.isClockButtonDisabled()) {
          return;
        }

        // 更新当前时间
        this.updateTime();

        // 显示加载提示
        uni.showLoading({
          title: '获取位置信息...',
          mask: true
        });

        let location;
        try {
          // 优先使用高德地图定位
          // location = await this.getAMapLocation();
          location = await this.getUniAppLocation();
          console.log('高德地图定位成功：', JSON.stringify(location, null, 6));
        } catch (error) {
          // 如果高德地图定位失败，尝试备选方案，不做备选，默认用班次数据设置的经纬度
          try {
            //location = await this.getAlternativeLocation();
          } catch (altError) {
            uni.showToast({
              title: '无法获取位置，将使用默认坐标',
              icon: 'none'
            });

            // 使用默认坐标,用户在排班信息设置的经纬度
            location = {
              latitude: undefined,
              longitude: undefined,
              type: 'DEFAULT'
            };
          }
        }

        // 构建打卡数据
        const clockData = {
          ...(this.clockType === 'clockIn'
                  ? {
                    clockInLatitude: location.latitude,
                    clockInLongitude: location.longitude,
                    clockInTime: this.currentTime,
                    locationType: location.type || 'BROWSER',
                    clockDataType: 'clockIn'
                  }
                  : {
                    clockOutLatitude: location.latitude,
                    clockOutLongitude: location.longitude,
                    clockOutTime: this.currentTime,
                    locationType: location.type || 'BROWSER',
                    clockDataType: 'clockOut'
                  }
          )
        };

        // 如果存在recordId，添加到请求数据
        if (this.attendanceRecord.recordId) {
          clockData.recordId = this.attendanceRecord.recordId;
        }

        // 更新加载提示
        uni.showLoading({
          title: '打卡中...',
          mask: true
        });

        // 调用打卡接口
        const res = await (this.attendanceRecord.recordId ? update(clockData) : add(clockData));

        // 隐藏加载提示
        uni.hideLoading();

        if (res.code === 200) {
          // 更新本地打卡记录
          if (this.clockType === 'clockIn') {
            this.attendanceRecord = {
              ...this.attendanceRecord,
              clockInTime: this.currentTime,
              clockInLatitude: location.latitude,
              clockInLongitude: location.longitude
            };
          } else {
            this.attendanceRecord = {
              ...this.attendanceRecord,
              clockOutTime: this.currentTime,
              clockOutLatitude: location.latitude,
              clockOutLongitude: location.longitude
            };
          }

          // 如果是新增操作，保存返回的recordId
          if (!this.attendanceRecord.recordId && res.data) {
            this.attendanceRecord.recordId = res.data.recordId;
          }

          // 更新打卡状态
          this.updateClockStatus();

          // 重新获取打卡数据
          await this.getItsmAttendanceSchedulingInfo();

          // 显示成功提示
          uni.showToast({
            title: `${this.clockType === 'clockIn' ? '上班' : '下班'}打卡成功`,
            icon: 'success',
            duration: 2000
          });
        } else {
          throw new Error(res.msg || '打卡失败');
        }
      } catch (error) {
        console.error('打卡失败：', error);
        uni.hideLoading();
        uni.showToast({
          title: error.message || '打卡失败',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 获取时间状态样式类
    getTimeStatusClass(type) {
      if (type === 'in') {
        if (!this.attendanceRecord.clockInTime) {
          return this.currentTime > this.shiftInfo.punchInTime ? 'status-error' : 'status-pending';
        }
        return this.attendanceRecord.clockInTime > this.shiftInfo.punchInTime ? 'status-warning' : 'status-normal';
      } else {
        if (!this.attendanceRecord.clockOutTime) {
          if (this.currentTime < this.shiftInfo.punchOutTime) {
            return 'status-pending';
          } else if (this.currentTime === this.shiftInfo.punchOutTime) {
            return 'status-warning';
          } else {
            return 'status-error';
          }
        }
        return this.attendanceRecord.clockOutTime < this.shiftInfo.punchOutTime ? 'status-warning' : 'status-normal';
      }
    },

    // 获取时间状态提示
    getTimeStatus(type) {
      const now = new Date();
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();
      const currentTime = `${String(currentHour).padStart(2, '0')}:${String(currentMinute).padStart(2, '0')}`;

      if (type === 'in') {
        if (!this.attendanceRecord.clockInTime) {
          return currentTime > this.shiftInfo.punchInTime ? '超时' : '未打卡';
        }
        return this.attendanceRecord.clockInTime > this.shiftInfo.punchInTime ? '迟到' : '正常';
      } else {
        if (!this.attendanceRecord.clockOutTime) {
          if (currentTime < this.shiftInfo.punchOutTime) {
            return '未打卡';
          } else if (currentTime === this.shiftInfo.punchOutTime) {
            return '可打卡';
          } else {
            return '超时';
          }
        }
        return this.attendanceRecord.clockOutTime < this.shiftInfo.punchOutTime ? '早退' : '正常';
      }
    },

    // 判断是否超过中午12点
    isAfterNoon() {
      const now = new Date();
      const currentHour = now.getHours();
      return currentHour >= 12;
    },

    // 获取上班打卡状态文本
    getClockInStatusText() {
      if (!this.isDataLoaded || !this.shiftInfo.shiftName) {
        return '无需打卡';
      }

      if (!this.isInWorkingHours) {
        return '非考勤时间';
      }

      if (this.isAfterNoon() && !this.attendanceRecord.clockInTime) {
        return '上班打卡已过期';
      }

      if (this.attendanceRecord.clockInTime) {
        const scheduledTime = this.shiftInfo.punchInTime;
        const actualTime = this.attendanceRecord.clockInTime;

        if (this.compareTime(actualTime, scheduledTime) > 0) {
          return `已打卡 ${actualTime} (迟到)`;
        } else {
          return `已打卡 ${actualTime} (正常)`;
        }
      }

      return '未打卡';
    },

    // 获取下班打卡状态文本
    getClockOutStatusText() {
      if (!this.isDataLoaded || !this.shiftInfo.shiftName) {
        return '无需打卡';
      }

      if (!this.isInWorkingHours) {
        return '非考勤时间';
      }

      // 如果已有下班打卡时间，显示打卡状态
      if (this.attendanceRecord.clockOutTime) {
        const scheduledTime = this.shiftInfo.punchOutTime;
        const actualTime = this.attendanceRecord.clockOutTime;

        if (this.compareTime(actualTime, scheduledTime) < 0) {
          return `已打卡 ${actualTime} (早退)`;
        } else {
          return `已打卡 ${actualTime} (正常)`;
        }
      }

      // 如果未到下班打卡时间（12点前）
      if (!this.isAfterNoon()) {
        return '未到打卡时间';
      }

      // 其他情况显示未打卡
      return '未打卡';
    },

    // 时间比较工具方法
    compareTime(time1, time2) {
      // 将时间符串转换为分钟数进行比较
      const getMinutes = (timeStr) => {
        const [hours, minutes] = timeStr.split(':').map(Number);
        return hours * 60 + minutes;
      };

      return getMinutes(time1) - getMinutes(time2);
    },

    // 判断是否可以下班打卡
    canClockOut() {
      // 如果已有下班打卡时间，不允许再次打卡
      if (this.attendanceRecord.clockOutTime) {
        return false;
      }

      // 必须超过中午12点
      if (!this.isAfterNoon()) {
        return false;
      }

      return true;
    },

    // 新增：设置默认打卡类型的方法
    setDefaultClockType() {
      const now = new Date();
      const currentHour = now.getHours();
      if (currentHour >= 12) {
        this.clockType = 'clockOut'; // 超过中午12点，默认选中下班打卡
      } else {
        this.clockType = 'clockIn'; // 中午12点之前，默认选中上班打卡
      }
    },
  },

  mounted() {
    this.updateTime(); // 组件挂载后立即更新时间
    this.timer = setInterval(this.updateTime, 1000);

    // 获取班次数据
    this.getShiftInfo(); 
    this.getItsmAttendanceSchedulingInfo(); // 获取当前用户的考勤数据

    // 新增：根据当前时间设置默认打卡类型
    this.setDefaultClockType();
  },

  destroyed() {
    clearInterval(this.timer); // 组件销毁前清除时器
  }
}
</script>

<style lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.header-section {
  background: linear-gradient(135deg, #3c96f3, #2979ff);
  padding: 40rpx 30rpx 60rpx;
  border-radius: 0 0 30rpx 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(41, 121, 255, 0.2);

  .user-info-container {
    display: flex;
    align-items: center;

    .user-avatar-section {
      .cu-avatar {
        background: rgba(255, 255, 255, 0.9);
        border: 4rpx solid rgba(255, 255, 255, 0.8);
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

        .icon-people {
          font-size: 60rpx;
          color: #3c96f3;
        }
      }
    }

    .user-details {
      flex: 1;
      margin-left: 24rpx;

      .user-name {
        font-size: 36rpx;
        color: #fff;
        font-weight: 600;
        margin-bottom: 8rpx;
      }

      .department-info {
        font-size: 26rpx;
        color: rgba(255, 255, 255, 0.9);
      }

      .login-tip {
        font-size: 32rpx;
        color: #fff;
      }
    }

    .profile-link {
      display: flex;
      align-items: center;
      padding: 12rpx 20rpx;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 30rpx;

      text {
        color: #fff;
        font-size: 26rpx;
        margin-right: 4rpx;
      }
    }
  }
}

.attendance-section {
  margin: -30rpx 24rpx 0;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);

  &.non-working-hours {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.65);
      backdrop-filter: blur(2px);
      pointer-events: none;
      z-index: 1;
      border-radius: inherit;
      transition: all 0.3s ease;
    }

    .shift-info-card,
    .clock-actions,
    .clock-button-section,
    .non-working-hours-tip {
      position: relative;
      z-index: 2;
      -webkit-font-smoothing: antialiased;
      text-rendering: optimizeLegibility;
    }

    .clock-button-section {
      .clock-button {
        &.button-disabled {
          background: linear-gradient(135deg, #bfbfbf, #999999);
          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);

          .button-text {
            color: #ffffff;
            font-weight: 500;
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
          }
        }
      }

      .records-link {
        background: rgba(0, 122, 255, 0.12);
        border: 1px solid rgba(0, 122, 255, 0.15);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);

        text {
          color: #007AFF;
          font-weight: 500;
        }
      }
    }

    .clock-actions {
      .clock-card {
        &.disabled {
          background: rgba(250, 250, 250, 0.95);
          border: 1px solid rgba(0, 0, 0, 0.04);
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);

          .card-content {
            .card-title {
              color: #333;
              font-weight: 500;
            }

            .card-status {
              color: #666;
            }
          }
        }
      }
    }
  }

  .shift-info-card {
    background: #f8f9fc;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 24rpx;

    .shift-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .shift-title {
        display: flex;
        align-items: center;
        gap: 12rpx;

        .shift-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
        }

        .attendance-status {
          font-size: 22rpx;
          padding: 4rpx 12rpx;
          border-radius: 20rpx;
          background-color: rgba(140, 140, 140, 0.1);
          color: #8c8c8c;

          &.status-active {
            background-color: rgba(82, 196, 26, 0.1);
            color: #52c41a;
          }
        }
      }

      .working-hours {
        font-size: 24rpx;
        color: #007AFF;
        background: rgba(0, 122, 255, 0.1);
        padding: 6rpx 16rpx;
        border-radius: 20rpx;
      }
    }

    .shift-details {
      .info-row {
        display: flex;
        margin-bottom: 20rpx;

        .label {
          font-size: 26rpx;
          color: #666;
          width: 120rpx;
        }

        .value {
          font-size: 26rpx;
          color: #333;
        }
      }

      .attendance-time-info {
        margin-top: 20rpx;
        background: #fff;
        border-radius: 12rpx;
        padding: 20rpx;

        .time-section {
          &:not(:last-child) {
            margin-bottom: 16rpx;
            padding-bottom: 16rpx;
            border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
          }

          .section-title {
            font-size: 24rpx;
            color: #666;
            margin-bottom: 8rpx;
          }

          .time-range {
            font-size: 30rpx;
            color: #333;
            font-weight: 500;

            .separator {
              margin: 0 16rpx;
              color: #999;
            }
          }

          .punch-times {
            display: flex;
            justify-content: space-between;
            gap: 24rpx;

            .punch-time {
              flex: 1;
              text-align: center;
              padding: 12rpx;
              background: #f8f9fc;
              border-radius: 8rpx;

              .time-label {
                font-size: 24rpx;
                color: #666;
                margin-bottom: 4rpx;
                display: block;
              }

              .time-value {
                font-size: 30rpx;
                color: #333;
                font-weight: 500;
              }
            }
          }
        }
      }
    }

    .no-schedule {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40rpx 0;
      color: #999;
      font-size: 28rpx;

      .uni-icons {
        margin-right: 8rpx;
      }
    }
  }

  .clock-actions {
    margin-bottom: 32rpx;
    transition: opacity 0.3s ease;

    &.disabled-actions {
      opacity: 0.6;
      pointer-events: none;
    }

    .clock-card {
      margin: 0 10rpx;
      background: #fff;
      border: 2rpx solid #eee;
      border-radius: 16rpx;
      padding: 24rpx;
      transition: all 0.3s ease;

      &.active {
        border-color: #007AFF;
        background: rgba(0, 122, 255, 0.05);

        .card-content {
          .card-title, .card-status {
            color: #007AFF;
          }
        }
      }

      &.disabled {
        opacity: 1;
        background: #f5f5f5;
        border-color: #e8e8e8;

        .card-content {
          .card-title {
            color: #666;
            font-weight: 500;
          }

          .card-status {
            color: #999;
          }
        }
      }

      .card-content {
        text-align: center;

        .card-title {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 8rpx;
          display: block;
        }

        .card-status {
          font-size: 26rpx;
          color: #666;
          margin-top: 8rpx;
          display: block;

          &.status-done {
            &.status-late {
              color: #ff4d4f;
            }

            &.status-early {
              color: #faad14;
            }

            &.status-normal {
              color: #52c41a;
            }
          }
        }
      }
    }
  }

  .clock-button-section {
    text-align: center;
    padding: 20rpx 0;

    .clock-button {
      width: 180rpx;
      height: 180rpx;
      border-radius: 90rpx;
      background: linear-gradient(135deg, #52c41a, #389e0d);
      color: #fff;
      font-size: 28rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 24rpx;
      border: none;
      box-shadow: 0 6rpx 16rpx rgba(82, 196, 26, 0.2);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }

      &.button-disabled {
        background: linear-gradient(135deg, #bfbfbf, #999999);
        opacity: 1;
        box-shadow: none;

        .button-text {
          color: #ffffff;
          font-weight: 500;
          text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
        }
      }

      .button-text {
        text-align: center;
        line-height: 1.4;
      }
    }

    .records-link {
      display: inline-flex;
      align-items: center;
      color: #666;
      font-size: 26rpx;
      padding: 12rpx 24rpx;
      background: rgba(0, 0, 0, 0.03);
      border-radius: 24rpx;
      opacity: 0.9;

      text {
        margin-right: 4rpx;
        color: #666;
        font-weight: 500;
      }
    }
  }

  .non-working-hours-tip {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24rpx;
    background: rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.03);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
    margin: 20rpx;

    .uni-icons {
      margin-right: 12rpx;
      color: #333;
    }

    text {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }
  }
}

.time-status {
  font-size: 22rpx;
  padding: 2rpx 12rpx;
  border-radius: 12rpx;
  margin-top: 8rpx;
  display: inline-block;

  &.status-normal {
    background-color: rgba(82, 196, 26, 0.1);
    color: #52c41a;
  }

  &.status-warning {
    background-color: rgba(250, 173, 20, 0.1);
    color: #faad14;
  }

  &.status-error {
    background-color: rgba(245, 34, 45, 0.1);
    color: #f5222d;
  }

  &.status-pending {
    background-color: rgba(140, 140, 140, 0.15);
    color: #333;
    font-weight: 500;
  }
}

.punch-time {
  .time-value {
    display: block;
    margin: 4rpx 0;
  }
}
</style>
