import Vue from 'vue'
import App from './App'
import store from './store' // store
import plugins from './plugins' // plugins
import './permission' // permission

// 动态加载高德地图SDK
function loadAMapScript() {
  return new Promise((resolve, reject) => {
    console.log('开始加载高德地图SDK');

    // 检查是否已经加载过SDK
    if (window.AMap) {
      console.log('高德地图SDK已存在');
      resolve(window.AMap);
      return;
    }

    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.async = true;
    script.defer = true;
    script.src = `https://webapi.amap.com/maps?v=2.0&key=d1d21f6385b62c26a220d73de60892b1&plugin=AMap.Geolocation`;
    script.onerror = (error) => {
      console.error('高德地图SDK加载失败:', error);
      reject(error);
    };
    
    script.onload = () => {
      console.log('高德地图SDK脚本加载完成');
      // 确保高德地图SDK完全加载
      if (window.AMap) {
        console.log('高德地图SDK加载成功');
        resolve(window.AMap);
      } else {
        console.error('高德地图SDK加载失败：未找到AMap对象');
        reject(new Error('高德地图SDK加载失败'));
      }
    };

    document.head.appendChild(script);
  });
}

Vue.use(plugins)

Vue.config.productionTip = false
Vue.prototype.$store = store

App.mpType = 'app'

// Vue.prototype.$loadAMapScript = loadAMapScript;

const app = new Vue({
  ...App
  // ,
  // async created() {
  //   try {
  //     await loadAMapScript();
  //     console.log('高德地图SDK加载成功');
  //   } catch (error) {
  //     console.error('高德地图SDK加载失败:', error);
  //     uni.showToast({
  //       title: '地图服务加载失败',
  //       icon: 'none'
  //     });
  //   }
  // }
})

app.$mount()
