<template>
  <view>
<!--    <AttendanceCard />-->
    <dashboardIndex />
  </view>
</template>

<script>
import AttendanceCard from '@/pages/attendanceCard/attendanceCard.vue';
import dashboardIndex from '@/pages/dashboard/index.vue';

export default {
  components: {
    AttendanceCard,
    dashboardIndex
  }
};
</script>

<style>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.logo {
  height: 200rpx;
  width: 200rpx;
  margin: 200rpx auto 50rpx;
}

.text-area {
  display: flex;
  justify-content: center;
}

.title {
  font-size: 36rpx;
  color: #8f8f94;
}
</style>
