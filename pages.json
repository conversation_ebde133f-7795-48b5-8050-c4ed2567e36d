{
  "pages": [
    {
      "path": "pages/login",
      "style": {
        "navigationBarTitleText": "登录"
      }
    },
    {
      "path": "pages/register",
      "style": {
        "navigationBarTitleText": "注册"
      }
    },
    {
      "path": "pages/index",
      "style": {
        "navigationBarTitleText": "中山市统一运维平台移动端",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/work/index",
      "style": {
        "navigationBarTitleText": "工作台"
      }
    },
    {
      "path": "pages/mine/index",
      "style": {
        "navigationBarTitleText": "我的"
      }
    },
    {
      "path": "pages/mine/avatar/index",
      "style": {
        "navigationBarTitleText": "修改头像"
      }
    },
    {
      "path": "pages/mine/info/index",
      "style": {
        "navigationBarTitleText": "个人信息"
      }
    },
    {
      "path": "pages/mine/info/edit",
      "style": {
        "navigationBarTitleText": "编辑资料"
      }
    },
    {
      "path": "pages/mine/pwd/index",
      "style": {
        "navigationBarTitleText": "修改密码"
      }
    },
    {
      "path": "pages/mine/setting/index",
      "style": {
        "navigationBarTitleText": "应用设置"
      }
    },
    {
      "path": "pages/mine/logout/index",
      "style": {
        "navigationBarTitleText": "退出登录"
      }
    },
    {
      "path": "pages/mine/help/index",
      "style": {
        "navigationBarTitleText": "常见问题"
      }
    },
    {
      "path": "pages/mine/about/index",
      "style": {
        "navigationBarTitleText": "关于我们"
      }
    },
    {
      "path": "pages/common/webview/index",
      "style": {
        "navigationBarTitleText": "浏览网页"
      }
    },
    {
      "path": "pages/common/textview/index",
      "style": {
        "navigationBarTitleText": "浏览文本"
      }
    },
    {
      "path": "pages/mine/leave/index",
      "style": {
        "navigationBarTitleText": "请假"
      }
    },
    {
      "path": "pages/mine/workOvertime/index",
      "style": {
        "navigationBarTitleText": "加班"
      }
    },
    {
      "path": "pages/mine/goOut/index",
      "style": {
        "navigationBarTitleText": "外出"
      }
    },
    {
      "path": "pages/attendanceCard/record/index",
      "style": {
        "navigationBarTitleText": "打卡记录",
        "enablePullDownRefresh": true,
        "backgroundTextStyle": "dark",
        "backgroundColor": "#f5f5f5"
      }
    },
    {
      "path": "pages/dashboard/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/dashboard/person",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/dashboard/system",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/dashboard/server",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/dashboard/ticket",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/dashboard/assets",
      "style": {
        "navigationStyle": "custom"
      }
    }
  ],
//  "tabBar": {
//    "color": "#000000",
//    "selectedColor": "#000000",
//    "borderStyle": "white",
//    "backgroundColor": "#ffffff",
//    "list": [
//      {
//        "pagePath": "pages/index",
//        "iconPath": "static/images/tabbar/home.png",
//        "selectedIconPath": "static/images/tabbar/home_.png",
//        "text": "首页"
//      }
////      {
////        "pagePath": "pages/work/index",
////        "iconPath": "static/images/tabbar/work.png",
////        "selectedIconPath": "static/images/tabbar/work_.png",
////        "text": "工作台"
////      }
////      {
////        "pagePath": "pages/mine/index",
////        "iconPath": "static/images/tabbar/mine.png",
////        "selectedIconPath": "static/images/tabbar/mine_.png",
////        "text": "我的"
////      }
//    ]
//  },
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "RuoYi",
    "navigationBarBackgroundColor": "#FFFFFF"
  },
  "usingComponents": {
    "van-datetime-picker": "@vant/weapp/datetime-picker/index",
    "van-popup": "@vant/weapp/popup/index"
  }
}
