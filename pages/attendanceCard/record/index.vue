<template>
  <view class="record-container">
    <!-- 顶部标题栏 -->
    <view class="header">
      <view class="header-title">打卡记录</view>
    </view>

    <!-- 记录列表 -->
    <scroll-view
      scroll-y
      class="record-list"
      @scrolltolower="onReachBottom"
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onPullDownRefresh"
    >
      <view v-for="(item, index) in records" :key="index" class="record-item">
        <view class="record-content">
          <!-- 日期和用户信息 -->
          <view class="record-header">
            <view class="header-left">
              <view class="date-info">
                <text class="date">{{ formatDate(item.createTime) }}</text>
                <text class="week">{{ getWeekDay(item.createTime) }}</text>
              </view>
              <text class="user-name">{{ item.userName || '未知用户' }}</text>
            </view>
          </view>

          <!-- 打卡详情 -->
          <view class="record-details">
            <!-- 上班打卡 -->
            <view class="clock-section">
              <view class="clock-header">
                <text class="label">上班打卡</text>
                <view class="status-tag" :class="getStatusClass(item.clockInStatus)">
                  {{ getStatusText(item.clockInStatus) }}
                </view>
              </view>
              <view class="time-block">
                <text class="time" :class="getTimeClass(item.clockInStatus)">
                  {{ item.clockInTime || '未打卡' }}
                </text>
              </view>
              <view class="location-block">
                <uni-icons type="location-filled" size="14" color="#999"></uni-icons>
                <text class="location-text">{{ item.clockInLocationName || '中山公共资源交易中心' }}</text>
              </view>
            </view>

            <!-- 分隔线 -->
            <view class="divider"></view>

            <!-- 下班打卡 -->
            <view class="clock-section">
              <view class="clock-header">
                <text class="label">下班打卡</text>
                <view class="status-tag" :class="getStatusClass(item.clockOutStatus)">
                  {{ getStatusText(item.clockOutStatus) }}
                </view>
              </view>
              <view class="time-block">
                <text class="time" :class="getTimeClass(item.clockOutStatus)">
                  {{ item.clockOutTime || '未打卡' }}
                </text>
              </view>
              <view class="location-block">
                <uni-icons type="location-filled" size="14" color="#999"></uni-icons>
                <text class="location-text">{{ item.clockOutLocationName || '中山公共资源交易中心' }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态优化 -->
      <view v-if="!records.length" class="empty-state">
        <image src="/static/images/empty.png" mode="aspectFit" class="empty-image"></image>
        <text class="empty-text">暂无打卡记录~</text>
      </view>

      <!-- 加载更多状态 -->
      <uni-load-more
        :status="loadMoreStatus"
        :icon-size="16"
        :content-text="loadMoreConfig"
      ></uni-load-more>
    </scroll-view>
  </view>
</template>

<script>
import { list } from '@/api/itsm/attendance';

export default {
  data() {
    return {
      pageNum: 1,
      pageSize: 10,
      total: 0,
      records: [],
      loadMoreStatus: 'more',
      isRefreshing: false,
      isLoading: false,
      loadMoreConfig: {
        contentdown: '上拉加载更多',
        contentrefresh: '加载中...',
        contentnomore: '没有更多数据了'
      }
    }
  },

  methods: {
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 修改状态样式类方法
    getStatusClass(status) {
      const statusMap = {
        '-2': 'status-early',    // 早退
        '-1': 'status-late',     // 迟到
        '0': 'status-no-record', // 未打卡
        '1': 'status-normal',    // 正常
        '2': 'status-finish',    // 已打卡
        '3': 'status-leave',     // 请假
        '4': 'status-out'        // 外出
      };
      return statusMap[status] || 'status-no-record';
    },

    // 修改状态文本方法
    getStatusText(status) {
      const statusMap = {
        '-2': '早退',
        '-1': '迟到',
        '0': '未打卡',
        '1': '正常',
        '2': '已打卡',
        '3': '请假',
        '4': '外出'
      };
      return statusMap[status] || '未打卡';
    },

    // 修改时间样式类方法
    getTimeClass(status) {
      return {
        'time-early': status === '-2',
        'time-late': status === '-1',
        'time-no-record': status === '0',
        'time-normal': status === '1',
        'time-finish': status === '2',
        'time-leave': status === '3',
        'time-out': status === '4'
      };
    },

    // 获取打卡记录列表
    async getRecordList(isLoadMore = false) {
      if (this.isLoading) return;
      this.isLoading = true;
      this.loadMoreStatus = 'loading';

      try {
        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize
        };

        const res = await list(params);

        if (res.code === 200) {
          const { rows, total } = res;

          if (isLoadMore) {
            this.records = [...this.records, ...rows];
          } else {
            this.records = rows;
          }

          this.total = total;
          this.loadMoreStatus = this.records.length >= total ? 'noMore' : 'more';
        } else {
          uni.showToast({
            title: res.msg || '获取数据失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取打卡记录失败:', error);
        uni.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
        if (this.isRefreshing) {
          this.isRefreshing = false;
          uni.stopPullDownRefresh();
        }
      }
    },

    // 下拉刷新
    onPullDownRefresh() {
      this.isRefreshing = true;
      this.pageNum = 1;
      this.records = [];
      this.getRecordList();
    },

    // 上拉加载更多
    onReachBottom() {
      if (this.loadMoreStatus === 'noMore' || this.isLoading) return;
      this.pageNum++;
      this.getRecordList(true);
    },

    // 获取星期几
    getWeekDay(dateString) {
      if (!dateString) return '';
      const weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      const date = new Date(dateString);
      return weeks[date.getDay()];
    }
  },

  onLoad() {
    this.getRecordList();
  }
}
</script>

<style lang="scss" scoped>
.record-container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding: 24rpx;

  .header {
    margin-bottom: 24rpx;
    padding: 20rpx 0;

    .header-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .record-list {
    .record-item {
      background: #ffffff;
      border-radius: 16rpx;
      margin-bottom: 20rpx;
      padding: 24rpx;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
      }

      .record-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 28rpx;

        .header-left {
          flex: 1;
          margin-right: 20rpx;

          .date-info {
            display: flex;
            align-items: baseline;
            margin-bottom: 8rpx;

            .date {
              font-size: 32rpx;
              color: #333;
              font-weight: 600;
              margin-right: 12rpx;
            }

            .week {
              font-size: 24rpx;
              color: #666;
            }
          }

          .user-name {
            font-size: 26rpx;
            color: #666;
            display: block;
            margin-top: 4rpx;
          }
        }

        .status-container {
          display: flex;
          gap: 8rpx;

          .status-tag {
            font-size: 22rpx;
            padding: 4rpx 12rpx;
            border-radius: 12rpx;

            &.status-no-record {
              background-color: rgba(140, 140, 140, 0.1);
              color: #8c8c8c;
            }

            &.status-normal {
              background-color: rgba(82, 196, 26, 0.1);
              color: #52c41a;
            }

            &.status-finish {
              background-color: rgba(82, 196, 26, 0.1);
              color: #52c41a;
            }

            &.status-leave {
              background-color: rgba(24, 144, 255, 0.1);
              color: #1890ff;
            }

            &.status-out {
              background-color: rgba(114, 46, 209, 0.1);
              color: #722ed1;
            }
          }
        }
      }

      .record-details {
        background-color: #f8f9fc;
        border-radius: 12rpx;
        margin-top: 16rpx;

        .clock-section {
          padding: 24rpx;

          .clock-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16rpx;

            .label {
              font-size: 28rpx;
              color: #333;
              font-weight: 500;
            }

            .status-tag {
              font-size: 24rpx;
              padding: 4rpx 16rpx;
              border-radius: 24rpx;
              font-weight: 500;

              &.status-early {
                background-color: rgba(245, 34, 45, 0.1);
                color: #f5222d;
              }

              &.status-late {
                background-color: rgba(250, 140, 22, 0.1);
                color: #fa8c16;
              }

              &.status-no-record {
                background-color: rgba(140, 140, 140, 0.1);
                color: #8c8c8c;
              }

              &.status-normal {
                background-color: rgba(82, 196, 26, 0.1);
                color: #52c41a;
              }

              &.status-finish {
                background-color: rgba(82, 196, 26, 0.1);
                color: #52c41a;
              }

              &.status-leave {
                background-color: rgba(24, 144, 255, 0.1);
                color: #1890ff;
              }

              &.status-out {
                background-color: rgba(114, 46, 209, 0.1);
                color: #722ed1;
              }
            }
          }

          .time-block {
            margin: 12rpx 0;

            .time {
              font-size: 36rpx;
              font-weight: 600;

              &.time-early {
                color: #f5222d;
              }

              &.time-late {
                color: #fa8c16;
              }

              &.time-no-record {
                color: #999;
                font-weight: normal;
                font-size: 32rpx;
              }

              &.time-normal,
              &.time-finish {
                color: #52c41a;
              }

              &.time-leave {
                color: #1890ff;
              }

              &.time-out {
                color: #722ed1;
              }
            }
          }

          .location-block {
            display: flex;
            align-items: center;
            margin-top: 8rpx;
            opacity: 0.8;

            .location-text {
              font-size: 26rpx;
              color: #666;
              margin-left: 8rpx;
            }
          }
        }

        .divider {
          height: 1rpx;
          background: linear-gradient(to right, transparent, #eee, transparent);
          margin: 0;
        }
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 120rpx 0;

    .empty-image {
      width: 240rpx;
      height: 240rpx;
      margin-bottom: 24rpx;
    }

    .empty-text {
      font-size: 28rpx;
      color: #999;
      background-color: rgba(0, 0, 0, 0.02);
      padding: 16rpx 40rpx;
      border-radius: 32rpx;
    }
  }
}
</style>
