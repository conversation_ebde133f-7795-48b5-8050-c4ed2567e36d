<template>
  <view class="page-container">

    <!-- 内容区域 -->
    <scroll-view
        :class="['content', { 'picker-visible': isPickerVisible }]"
        scroll-y=""
        @scrolltolower="loadMore"
        :refresher-triggered="isRefreshing"
        @refresherrefresh="onRefresh"
        refresher-enabled="true"
        lower-threshold="50"
    >
      <!-- 请假页面 -->
      <view v-if="activeTab === 0" class="leave-form">
        <!-- 页面头部 -->
        <view class="page-header">
          <view class="header-content">
            <view class="header-title">
              <text class="title-text">请假申请</text>
              <text class="title-subtitle">填写您的请假信息</text>
            </view>
            <view class="header-decoration">
              <view class="decoration-icon">🏖️</view>
            </view>
          </view>
        </view>

        <!-- 表单卡片 -->
        <view class="form-card">
          <uni-forms ref="form" :model="user" :rules="rules" class="modern-form">

            <!-- 请假类型选择 -->
            <view class="form-section">
              <view class="section-title">
                <view class="title-icon">📋</view>
                <text class="title-text">请假类型</text>
              </view>
              <view class="form-field">
                <picker :range="leaveTypes" @change="onLeaveTypeChange" :value="user.leaveType">
                  <view class="modern-input type-selector">
                    <view class="input-content">
                      <text class="input-text" :class="{ 'placeholder': !leaveTypes[user.leaveType] }">
                        {{ leaveTypes[user.leaveType] || '请选择请假类型' }}
                      </text>
                      <view class="input-arrow">
                        <text class="arrow-icon">›</text>
                      </view>
                    </view>
                  </view>
                </picker>
              </view>
            </view>

            <!-- 时间选择区域 -->
            <view class="form-section">
              <view class="section-title">
                <view class="title-icon">⏰</view>
                <text class="title-text">请假时间</text>
              </view>

              <view class="time-range-container">
                <!-- 开始时间 -->
                <view class="time-field">
                  <view class="time-label">
                    <text class="label-text">开始时间</text>
                    <text class="label-required">*</text>
                  </view>
                  <view class="form-field">
                    <DatetimePickerFix
                        type="datetime"
                        v-model="user.startDate"
                        @change="onStartDateChange"
                        @open="togglePickerVisibility(true)"
                        @close="togglePickerVisibility(false)"
                        placeholder="请选择开始时间"
                        title="选择开始时间"
                    />
                  </view>
                </view>

                <!-- 时间连接线 -->
                <view class="time-connector">
                  <view class="connector-line"></view>
                  <view class="connector-dot"></view>
                </view>

                <!-- 结束时间 -->
                <view class="time-field">
                  <view class="time-label">
                    <text class="label-text">结束时间</text>
                    <text class="label-required">*</text>
                  </view>
                  <view class="form-field">
                    <DatetimePickerFix
                        type="datetime"
                        v-model="user.endDate"
                        @change="onEndDateChange"
                        @open="togglePickerVisibility(true)"
                        @close="togglePickerVisibility(false)"
                        placeholder="请选择结束时间"
                        title="选择结束时间"
                    />
                  </view>
                </view>
              </view>

              <!-- 时长显示 -->
              <view v-if="user.startDate && user.endDate" class="duration-display">
                <view class="duration-card">
                  <view class="duration-icon">📅</view>
                  <view class="duration-info">
                    <text class="duration-text">请假时长</text>
                    <text class="duration-value">{{ calculateDuration(user.startDate, user.endDate) }}</text>
                  </view>
                </view>

                <!-- 详细时间信息 -->
                <view class="duration-details">
                  <view class="detail-item">
                    <text class="detail-label">工作时间：</text>
                    <text class="detail-value">{{ formatWorkTime() }}</text>
                  </view>
                  <view class="detail-item">
                    <text class="detail-label">计算说明：</text>
                    <text class="detail-value">按{{ workTimeConfig.workHoursPerDay }}小时工作制计算</text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 附件上传区域 -->
            <view class="form-section">
              <view class="section-title">
                <view class="title-icon">📎</view>
                <text class="title-text">相关附件</text>
                <text class="title-optional">（可选）</text>
              </view>

              <view class="upload-area">
                <!-- 自定义上传按钮 -->
                <view class="custom-upload-container">
                  <view class="upload-button-wrapper" @click="triggerFileUpload">
                    <view class="upload-button-content">
                      <view class="upload-icon-large">
                        <text class="icon-plus">+</text>
                      </view>
                      <view class="upload-text-content">
                        <text class="upload-main-text">添加附件</text>
                        <text class="upload-sub-text">支持图片格式</text>
                      </view>
                    </view>
                  </view>

                  <!-- 已上传文件列表 -->
                  <view v-if="user.attachment && user.attachment.length > 0" class="uploaded-files">
                    <view
                      v-for="(file, index) in user.attachment"
                      :key="index"
                      class="file-item"
                    >
                      <view class="file-preview">
                        <image :src="file.url" mode="aspectFill" class="file-image"></image>
                        <view class="file-remove" @click="removeFile(index)">
                          <text class="remove-icon">×</text>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>

                <!-- 隐藏的原生文件选择器 -->
                <uni-file-picker
                  v-model="user.attachment"
                  fileMediatype="image"
                  :limit="3"
                  @select="onFileSelect"
                  mode="grid"
                  class="hidden-file-picker"
                  ref="filePicker"
                >
                </uni-file-picker>
              </view>
            </view>

          </uni-forms>
        </view>

        <!-- 提交按钮 -->
        <view class="submit-section">
          <button class="modern-submit-button" @click="submit">
            <view class="button-content">
              <text class="button-text">提交申请</text>
              <view class="button-icon">✓</view>
            </view>
          </button>
        </view>
      </view>

      <!-- 查看历史页面 -->
      <view v-else class="history-list">
        <view v-for="(item, index) in leaveHistory" :key="index" class="record-item">
          <view class="record-content">
            <view class="record-main">
              <view class="record-header">
                <view class="user-info">
                  <text class="user-name">{{ item.userName }}</text>
                  <text class="time-duration">{{ calculateDuration(item.startTime, item.endTime) }}</text>
                </view>
<!--                <view class="record-status" :class="getStatusClass(item)">-->
<!--                  {{ getStatusText(item.processingStatus) }}-->
<!--                </view>-->
              </view>
              
              <view class="record-details">
                <view class="detail-row">
                  <text class="detail-label">请假类型：</text>
                  <text class="detail-value">{{ getLeaveType(item.type) }}</text>
                </view>
                <view class="detail-row">
                  <text class="detail-label">开始时间：</text>
                  <text class="detail-value">{{ formatDateTime(item.startTime) }}</text>
                </view>
                <view class="detail-row">
                  <text class="detail-label">结束时间：</text>
                  <text class="detail-value">{{ formatDateTime(item.endTime) }}</text>
                </view>
                <view class="detail-row reason-row" v-if="item.reason">
                  <text class="detail-label">请假事由：</text>
                  <text class="detail-value reason-text">{{ item.reason }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="!leaveHistory.length" class="empty-state">
          <text class="empty-text">暂无请假记录</text>
        </view>

        <!-- 加载更多状态 -->
        <uni-load-more :status="loadingMore ? 'loading' : (pageNo >= totalPages ? 'noMore' : 'more')"></uni-load-more>
      </view>

    </scroll-view>

    <!-- TabBar -->
    <view v-if="showTabBar" class="custom-tab-bar">
      <view
          class="tab-item"
          :class="{ active: activeTab === 0 }"
          @click="switchTab(0)"
      >
        <view class="tab-icon">📝</view>
        <text class="tab-text">申请请假</text>
      </view>
      <view
          class="tab-item"
          :class="{ active: activeTab === 1 }"
          @click="switchTab(1)"
      >
        <view class="tab-icon">📋</view>
        <text class="tab-text">查看记录</text>
      </view>
    </view>

  </view>
</template>

<script>

import {addLeave, list, uploadFile} from "@/api/itsm/leave";
import {completeTask, startWorkFlow} from "@/api/itsm/workOvertime";
import DatetimePickerFix from '@/components/datetime-picker-fix/datetime-picker-fix.vue';

export default {
  components: {
    DatetimePickerFix
  },
  data() {
    return {
      leaveTypes: ['请选择', '年假', '事假', '病假', '婚假', '调休'],
      leaveTypeMapping: {
        '年假': 1,
        '事假': 2,
        '病假': 3,
        '婚假': 4,
        '调休': 5
      },
      user: {
        leaveType: undefined,
        startDate: '',
        endDate: '',
        attachment: [],
        fileIds: [], // 存储文件 ID
        fileId: ''
      },
      rules: {
        leaveType: {
          rules: [{
            required: true,
            errorMessage: '请假类型不能为空'
          }]
        },
        startDate: {
          rules: [{
            required: true,
            errorMessage: '开始时间不能为空'
          }]
        },
        endDate: {
          rules: [{
            required: true,
            errorMessage: '结束时间不能为空'
          }]
        }
      },
      maxAttachmentCount: 3, // 限制最多上传的图片数量

      activeTab: 0, // 标签页索引
      listData: [], // 列表数据


      historyList: [],
      loading: false,
      noMoreData: false,
      showTabBar: true, // 控制 TabBar 显示或隐藏

      isPickerVisible: false, // 新增状态，用于控制 Picker 是否可见

      leaveHistory: [], // 存储请假历史记录
      pageNo: 1, // 当前页码
      pageSize: 10, // 每页显示条目数
      totalPages: 0, // 总页数
      isRefreshing: false, // 是否正在刷新
      loadingMore: false, // 是否正在加载更多

      leaveConverTypes: {
        '1': '年假',
        '2': '事假',
        '3': '病假',
        '4': '婚假',
        '5': '调休'
      },

      // 工作时间配置
      workTimeConfig: {
        morningStart: { hour: 8, minute: 30 },   // 上午开始：8:30
        morningEnd: { hour: 12, minute: 0 },     // 上午结束：12:00
        afternoonStart: { hour: 13, minute: 30 }, // 下午开始：13:30
        afternoonEnd: { hour: 18, minute: 0 },   // 下午结束：18:00
        workHoursPerDay: 8,  // 每天工作小时数
        workDays: [1, 2, 3, 4, 5], // 工作日：周一到周五 (0=周日, 1=周一, ..., 6=周六)
        lunchBreak: {
          start: { hour: 12, minute: 0 },   // 午休开始：12:00
          end: { hour: 13, minute: 30 },    // 午休结束：13:30
          duration: 1.5      // 午休时长（小时）
        }
      }

    }
  },
  mounted() {
    this.$refs.form.setRules(this.rules)
  },

  watch: {
    activeTab(newValue) {
      if (newValue === 1) {
        // 如果切换到历史记录页，则重新获取第一页的数据
        this.onRefresh();
      }
    }
  },

  methods: {
    // 获取请假历史记录
    async fetchLeaveHistory(pageNo = this.pageNo) {
      const queryParams = {pageNum: 1}
      list(queryParams).then(response => {
            console.log('获取后端分页请假记录结果：', JSON.stringify(response, null, 6))
            if (response.code === 200){
              const { rows, total } = response;
              this.leaveHistory =  rows;
              console.log('this.leaveHistory :' + JSON.stringify(this.leaveHistory,null,6));
              this.totalPages = Math.ceil(total / this.pageSize);
            }
          }
      );
    },

    onRefresh() {
      this.isRefreshing = true;
      this.pageNo = 1;
      this.fetchLeaveHistory().finally(() => {
        this.isRefreshing = false;
      });
    },

    // 上拉加载更多
    loadMore() {
      if (this.loadingMore || this.pageNo >= this.totalPages) return;
      this.loadingMore = true;
      this.pageNo++;
      this.fetchLeaveHistory(this.pageNo).finally(() => {
        this.loadingMore = false;
      });
    },

    onLeaveTypeChange(e) {
      // console.log('输出一下请假类型：' + JSON.stringify(e, null, 6))
      // 获取选中值的索引
      this.user.leaveType = Number(e.detail.value); // 更新为索引值
    },

    onStartDateChange(e) {
      this.user.startDate = e
    },

    onEndDateChange(e) {
      this.user.endDate = e
    },

    // 触发文件上传
    triggerFileUpload() {
      // 检查文件数量限制
      if (this.user.attachment.length >= 3) {
        uni.showToast({
          title: '最多只能上传3张图片',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 使用uni.chooseImage来选择图片
      uni.chooseImage({
        count: 3 - this.user.attachment.length, // 剩余可选择数量
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.handleSelectedFiles(res.tempFilePaths);
        },
        fail: (err) => {
          console.error('选择图片失败:', err);
        }
      });
    },

    // 处理选中的文件
    handleSelectedFiles(filePaths) {
      // 添加到附件列表
      const newFiles = filePaths.map(path => ({ url: path }));
      this.user.attachment = [...this.user.attachment, ...newFiles];

      // 上传文件到服务器
      filePaths.forEach(filePath => {
        const data = {
          fileType: 'leaveInfo',
          filePath: filePath,
          name: 'file'
        };

        uploadFile(data).then(response => {
          console.log('APP端上传请假附件结果：', JSON.stringify(response, null, 6));
          if (response.code === 200) {
            this.user.fileId = response.msg;
            uni.showToast({
              title: "文件上传成功",
              icon: 'success',
              duration: 1500
            });
          } else {
            uni.showToast({
              title: "文件上传失败",
              icon: 'none',
              duration: 2000
            });
          }
        }).catch(err => {
          console.error('上传文件失败:', err);
          uni.showToast({
            title: "文件上传失败",
            icon: 'none',
            duration: 2000
          });
        });
      });
    },

    // 移除文件
    removeFile(index) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个附件吗？',
        success: (res) => {
          if (res.confirm) {
            this.user.attachment.splice(index, 1);
            uni.showToast({
              title: '删除成功',
              icon: 'success',
              duration: 1500
            });
          }
        }
      });
    },

    onFileSelect(e) {
      // 保留原有方法作为备用
      console.log('Selected files:', JSON.stringify(e.tempFiles, null, 6));
      this.handleSelectedFiles(e.tempFiles.map(file => file.path));
    },

    switchTab(tabIndex) {
      this.activeTab = tabIndex;
      if (tabIndex === 1) {
        this.onRefresh();
      }
    },

    getLeaveType(type) {
      return this.leaveConverTypes[type] || '未知';
    },

    togglePickerVisibility(visible) {
      this.isPickerVisible = visible;
    },

    submit() {
      this.$refs.form.validate().then(() => {
        const leaveRequest = {
          type: this.leaveTypeMapping[this.leaveTypes[this.user.leaveType]], // 使用映射获取对应值
          startTime: this.user.startDate,
          endTime: this.user.endDate,
          fileId: this.user.fileId,
          processingStatus: 'draft'
        };

        console.log('输出一下，传递到后端的请假申请表单参数：' + JSON.stringify(leaveRequest, null, 6));

        addLeave(leaveRequest).then(res => {
          console.log('APP端提交请假结果：' + JSON.stringify(res, null, 6));

          if (res.code === 200) {
            const submitFormData = {
              businessKey: res.data.leaveId,
              tableName: 'itsm_attendance_leave_log',
              variables: {
                entity: res.data,
                userList: [1, 3],
                userList2: [1, 3]
              }
            };
            //启动审批流
            startWorkFlow(submitFormData).then(workflowRes => {
              if (workflowRes.code === 200) {
                const taskForm = {
                  taskId: workflowRes.data.taskId,
                  taskVariables: {},
                  messageType: ['1'],
                  wfCopyList: []
                };

                completeTask(taskForm).then(taskRes => {
                  console.log('app端提交加班申请，工作流提交结果：' + JSON.stringify(taskRes, null, 6));

                  // 所有异步操作完成后才显示成功消息并返回
                  uni.showToast({ title: "提交成功", icon: 'success', duration: 2000 }); // 设置持续时间以确保提示可见

                  // 在成功消息显示一定时间后返回上一页
                  setTimeout(() => {
                    uni.navigateBack(); // 返回上一页
                  }, 2000); // 等待2秒再返回，确保提示框显示完毕
                }).catch(err => {
                  console.error('完成任务失败', err);
                  uni.showToast({ title: "提交失败", icon: 'none' });
                });
              } else {
                console.error('启动工作流失败', workflowRes);
                uni.showToast({ title: "提交失败", icon: 'none' });
              }
            }).catch(err => {
              console.error('启动工作流失败', err);
              uni.showToast({ title: "提交失败", icon: 'none' });
            });


            // 显示成功消息并设置持续时间为 2 秒
            uni.showToast({
              title: "提交成功",
              icon: 'success',
              duration: 2000 // 设置适当的时间，如2秒
            });

            // 清空表单数据
            this.user = {};

            // 延迟导航回上一页，确保消息框显示完毕
            setTimeout(() => {
              uni.navigateBack(); // 返回上一页
            }, 2000); // 等待2秒再返回，确保提示框显示完毕
          } else {
            uni.showToast({ title: "提交失败", icon: 'none' });
          }
        }).catch(err => {
          console.error('提交请假请求失败', err);
          uni.showToast({ title: "提交失败", icon: 'none' });
        });
      }).catch(err => {
        console.log('表单校验失败', err);
      });
    },

    // 按照8小时工作制计算请假时长
    calculateDuration(startTime, endTime) {
      if (!startTime || !endTime) return '未知时长';

      try {
        const start = new Date(startTime);
        const end = new Date(endTime);

        // 验证时间有效性
        if (isNaN(start.getTime()) || isNaN(end.getTime())) {
          return '时间格式错误';
        }

        // 如果结束时间早于开始时间
        if (end <= start) {
          return '时间范围错误';
        }

        // 使用配置的工作时间
        const workConfig = this.workTimeConfig;

        let totalWorkHours = 0;

        // 获取开始和结束日期（不含时间）
        const startDate = new Date(start.getFullYear(), start.getMonth(), start.getDate());
        const endDate = new Date(end.getFullYear(), end.getMonth(), end.getDate());

        // 调试信息
        console.log('计算请假时长:', {
          startTime: startTime,
          endTime: endTime,
          startDate: startDate.toDateString(),
          endDate: endDate.toDateString(),
          isSameDay: startDate.getTime() === endDate.getTime()
        });

        // 如果是同一天
        if (startDate.getTime() === endDate.getTime()) {
          totalWorkHours = this.calculateSameDayWorkHours(start, end, workConfig);
          console.log('同一天计算结果:', totalWorkHours);
        } else {
          // 跨天计算
          totalWorkHours = this.calculateMultiDayWorkHours(start, end, workConfig);
          console.log('跨天计算结果:', totalWorkHours);
        }

        // 格式化显示
        if (totalWorkHours <= 0) {
          return '0小时';
        } else if (totalWorkHours < 1) {
          const minutes = Math.round(totalWorkHours * 60);
          return `${minutes}分钟`;
        } else if (totalWorkHours < 8) {
          const hours = Math.floor(totalWorkHours);
          const minutes = Math.round((totalWorkHours - hours) * 60);
          return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`;
        } else {
          const days = Math.floor(totalWorkHours / 8);
          const remainingHours = totalWorkHours % 8;
          const hours = Math.floor(remainingHours);
          const minutes = Math.round((remainingHours - hours) * 60);

          let result = `${days}天`;
          if (hours > 0) result += `${hours}小时`;
          if (minutes > 0) result += `${minutes}分钟`;

          return result;
        }

      } catch (e) {
        console.error('计算请假时长错误:', e);
        return '计算错误';
      }
    },

    // 计算同一天内的工作小时数（支持上下午分段）
    calculateSameDayWorkHours(start, end, workConfig) {
      const dayOfWeek = start.getDay();

      console.log('同一天计算:', {
        date: start.toDateString(),
        dayOfWeek: dayOfWeek,
        isWorkDay: workConfig.workDays.includes(dayOfWeek),
        startTime: start.toTimeString(),
        endTime: end.toTimeString()
      });

      // 检查是否为工作日
      if (!workConfig.workDays.includes(dayOfWeek)) {
        console.log('非工作日，返回0');
        return 0; // 非工作日不计算工作时间
      }

      const dateBase = new Date(start.getFullYear(), start.getMonth(), start.getDate());

      // 定义上午和下午的工作时间段
      const morningStart = new Date(dateBase.getTime());
      morningStart.setHours(workConfig.morningStart.hour, workConfig.morningStart.minute, 0, 0);

      const morningEnd = new Date(dateBase.getTime());
      morningEnd.setHours(workConfig.morningEnd.hour, workConfig.morningEnd.minute, 0, 0);

      const afternoonStart = new Date(dateBase.getTime());
      afternoonStart.setHours(workConfig.afternoonStart.hour, workConfig.afternoonStart.minute, 0, 0);

      const afternoonEnd = new Date(dateBase.getTime());
      afternoonEnd.setHours(workConfig.afternoonEnd.hour, workConfig.afternoonEnd.minute, 0, 0);

      let totalHours = 0;

      // 计算上午时段的请假时间
      const morningLeaveStart = new Date(Math.max(start.getTime(), morningStart.getTime()));
      const morningLeaveEnd = new Date(Math.min(end.getTime(), morningEnd.getTime()));

      if (morningLeaveStart < morningLeaveEnd) {
        const morningMs = morningLeaveEnd.getTime() - morningLeaveStart.getTime();
        const morningHours = morningMs / (1000 * 60 * 60);
        totalHours += morningHours;
        console.log('上午时段:', morningLeaveStart.toTimeString(), '到', morningLeaveEnd.toTimeString(), '=', morningHours, '小时');
      }

      // 计算下午时段的请假时间
      const afternoonLeaveStart = new Date(Math.max(start.getTime(), afternoonStart.getTime()));
      const afternoonLeaveEnd = new Date(Math.min(end.getTime(), afternoonEnd.getTime()));

      if (afternoonLeaveStart < afternoonLeaveEnd) {
        const afternoonMs = afternoonLeaveEnd.getTime() - afternoonLeaveStart.getTime();
        const afternoonHours = afternoonMs / (1000 * 60 * 60);
        totalHours += afternoonHours;
        console.log('下午时段:', afternoonLeaveStart.toTimeString(), '到', afternoonLeaveEnd.toTimeString(), '=', afternoonHours, '小时');
      }

      console.log('当天总计:', totalHours, '小时');
      return totalHours;
    },

    // 计算跨多天的工作小时数
    calculateMultiDayWorkHours(start, end, workConfig) {
      let totalHours = 0;

      // 获取开始和结束日期
      const startDate = new Date(start.getFullYear(), start.getMonth(), start.getDate());
      const endDate = new Date(end.getFullYear(), end.getMonth(), end.getDate());

      console.log('跨天计算详情:', {
        startDate: startDate.toDateString(),
        endDate: endDate.toDateString(),
        startDay: startDate.getDay(),
        endDay: endDate.getDay()
      });

      // 1. 计算开始日期的剩余工作时间
      const startDayEnd = new Date(startDate.getTime());
      startDayEnd.setHours(workConfig.afternoonEnd.hour, workConfig.afternoonEnd.minute, 0, 0);

      const startDayHours = this.calculateSameDayWorkHours(start, startDayEnd, workConfig);
      totalHours += startDayHours;
      console.log('开始日工作时间:', startDayHours);

      // 2. 计算中间完整工作日的时间
      const currentDate = new Date(startDate);
      currentDate.setDate(currentDate.getDate() + 1); // 从第二天开始

      while (currentDate < endDate) {
        const dayOfWeek = currentDate.getDay();
        console.log('检查中间日期:', currentDate.toDateString(), '星期:', dayOfWeek);
        if (workConfig.workDays.includes(dayOfWeek)) {
          totalHours += workConfig.workHoursPerDay; // 完整工作日按8小时计算
          console.log('中间工作日加8小时');
        } else {
          console.log('中间非工作日跳过');
        }
        currentDate.setDate(currentDate.getDate() + 1);
      }

      // 3. 计算结束日期的工作时间（只有当结束日期不同于开始日期时）
      if (endDate.getTime() > startDate.getTime()) {
        const endDayStart = new Date(endDate.getTime());
        endDayStart.setHours(workConfig.morningStart.hour, workConfig.morningStart.minute, 0, 0);

        const endDayHours = this.calculateSameDayWorkHours(endDayStart, end, workConfig);
        totalHours += endDayHours;
        console.log('结束日工作时间:', endDayHours);
      }

      console.log('跨天计算总时间:', totalHours);
      return totalHours;
    },

    // 格式化工作时间显示
    formatWorkTime() {
      const config = this.workTimeConfig;
      const morningStart = `${config.morningStart.hour}:${config.morningStart.minute.toString().padStart(2, '0')}`;
      const morningEnd = `${config.morningEnd.hour}:${config.morningEnd.minute.toString().padStart(2, '0')}`;
      const afternoonStart = `${config.afternoonStart.hour}:${config.afternoonStart.minute.toString().padStart(2, '0')}`;
      const afternoonEnd = `${config.afternoonEnd.hour}:${config.afternoonEnd.minute.toString().padStart(2, '0')}`;

      return `${morningStart}-${morningEnd}, ${afternoonStart}-${afternoonEnd}`;
    },

    // 格式化日期时间
    formatDateTime(dateString) {
      if (!dateString) return '未设置';
      try {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (e) {
        return dateString;
      }
    },

    // 获取状态样式类
    getStatusClass(item) {
      if (!item || !item.processingStatus) return 'status-draft';
      const status = item.processingStatus;
      return `status-${status}`;
    },

    // 获取状态文本
    getStatusText(status) {
      if (!status) return '未知状态';
      const statusMap = {
        'cancel': '已撤销',
        'draft': '草稿',
        'waiting': '待审核',
        'finish': '已完成',
        'invalid': '已作废',
        'back': '已回退',
        'termination': '已终止',
        'inReview': '审核中'
      };
      return statusMap[status] || '未知状态';
    }

  },
}
</script>

<style lang="scss">
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: calc(98rpx + env(safe-area-inset-bottom));
}

/* 记录列表样式 */
.history-list {
  padding: 24rpx;
  background: transparent;
}

.record-item {
  background: rgba(255,255,255,0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
  border: 1px solid rgba(255,255,255,0.2);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.15);
  }
}

.record-content {
  .record-main {
    padding: 24rpx;

    .record-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;
      padding-bottom: 16rpx;
      border-bottom: 2rpx solid #f5f7fa;
      
      .user-info {
        display: flex;
        align-items: center;
        gap: 16rpx;
        
        .user-name {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
        }
        
        .time-duration {
          font-size: 24rpx;
          color: #666;
          background-color: #f8f9fc;
          padding: 4rpx 16rpx;
          border-radius: 24rpx;
        }
      }
      
      .record-status {
        font-size: 24rpx;
        padding: 6rpx 20rpx;
        border-radius: 24rpx;
        font-weight: 500;
        
        &.status-finish {
          background-color: rgba(82, 196, 26, 0.08) !important;
          color: #52c41a !important;
        }
        
        &.status-waiting {
          background-color: rgba(24, 144, 255, 0.08) !important;
          color: #1890ff !important;
        }
        
        &.status-draft {
          background-color: rgba(191, 191, 191, 0.08) !important;
          color: #8c8c8c !important;
        }
        
        &.status-invalid {
          background-color: rgba(245, 34, 45, 0.08) !important;
          color: #f5222d !important;
        }
        
        &.status-cancel {
          background-color: rgba(250, 173, 20, 0.08) !important;
          color: #faad14 !important;
        }
        
        &.status-back {
          background-color: rgba(114, 46, 209, 0.08) !important;
          color: #722ed1 !important;
        }
        
        &.status-termination {
          background-color: rgba(207, 19, 34, 0.08) !important;
          color: #cf1322 !important;
        }
        
        &.status-inReview {
          background-color: rgba(250, 173, 20, 0.08) !important;
          color: #faad14 !important;
        }
      }
    }

    .record-details {
      background-color: #fafbfc;
      border-radius: 16rpx;
      padding: 20rpx;
      
      .detail-row {
        display: flex;
        margin-bottom: 16rpx;
        align-items: flex-start;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        &.reason-row {
          margin-top: 16rpx;
          padding-top: 16rpx;
          border-top: 2rpx solid rgba(0,0,0,0.03);
        }

        .detail-label {
          width: 140rpx;
          color: #666;
          font-size: 26rpx;
          flex-shrink: 0;
        }

        .detail-value {
          flex: 1;
          color: #333;
          font-size: 26rpx;
          line-height: 1.5;
          
          &.reason-text {
            word-break: break-all;
            white-space: pre-wrap;
          }
        }
      }
    }
  }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
  
  .empty-text {
    color: #999;
    font-size: 28rpx;
    background-color: #f8f9fc;
    padding: 16rpx 32rpx;
    border-radius: 32rpx;
  }
}

/* 现代化TabBar样式 */
.custom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 98rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
  border-top: 1px solid rgba(255,255,255,0.2);
  z-index: 100;
  padding-bottom: env(safe-area-inset-bottom);

  .tab-item {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    padding: 8rpx 0; // 增加内边距
    transition: all 0.3s ease;
    
    &.active {
      .tab-icon {
        transform: scale(1.1);
        filter: drop-shadow(0 2rpx 8rpx rgba(102, 126, 234, 0.3));
      }

      .tab-text {
        color: #667eea;
        font-weight: 600;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 14rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 32rpx;
        height: 4rpx;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 2rpx;
        box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
      }
    }

    .tab-icon {
      font-size: 36rpx; // 稍微增大图标
      height: 36rpx;
      line-height: 36rpx;
      margin-bottom: 8rpx; // 增加图标和文字的间距
      transition: transform 0.2s ease;
    }

    .tab-text {
      font-size: 24rpx; // 稍微增大文字
      color: #666;
      line-height: 1.2; // 增加行高
      padding-bottom: 4rpx; // 添加文字底部间距
    }
  }
}

/* 相应调整内容区域的底部padding */
.content {
  padding-bottom: calc(98rpx + env(safe-area-inset-bottom));
}

.page-container {
  padding-bottom: calc(98rpx + env(safe-area-inset-bottom));
}

/* 内容区域样式 */
.content {
  flex: 1;
  overflow-y: auto;
  background: transparent;
  padding-bottom: calc(98rpx + env(safe-area-inset-bottom));

  &.refreshing {
    transition: transform 0.3s ease;
  }
}

/* 现代化请假表单样式 */
.leave-form {
  padding: 0 24rpx 40rpx;

  /* 页面头部样式 */
  .page-header {
    margin: 0 -24rpx 32rpx;
    padding: 40rpx 32rpx 32rpx;
    background: linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.05) 100%);
    backdrop-filter: blur(20rpx);
    border-radius: 0 0 32rpx 32rpx;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-title {
        .title-text {
          font-size: 48rpx;
          font-weight: 700;
          color: #fff;
          display: block;
          margin-bottom: 8rpx;
        }

        .title-subtitle {
          font-size: 28rpx;
          color: rgba(255,255,255,0.8);
          display: block;
        }
      }

      .header-decoration {
        .decoration-icon {
          font-size: 64rpx;
          opacity: 0.8;
        }
      }
    }
  }

  /* 表单卡片样式 */
  .form-card {
    background: #fff;
    border-radius: 24rpx;
    padding: 32rpx;
    box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
    margin-bottom: 32rpx;

    .modern-form {
      .form-section {
        margin-bottom: 40rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .section-title {
          display: flex;
          align-items: center;
          margin-bottom: 24rpx;

          .title-icon {
            font-size: 32rpx;
            margin-right: 12rpx;
          }

          .title-text {
            font-size: 32rpx;
            font-weight: 600;
            color: #333;
          }

          .title-optional {
            font-size: 24rpx;
            color: #999;
            margin-left: 8rpx;
          }
        }

        .form-field {
          .modern-input {
            background: #f8f9fc;
            border-radius: 16rpx;
            border: 2rpx solid transparent;
            transition: all 0.3s ease;
            overflow: hidden;

            &:active {
              border-color: #667eea;
              background: #f0f4ff;
            }

            &.type-selector {
              .input-content {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 24rpx 20rpx;

                .input-text {
                  font-size: 28rpx;
                  color: #333;

                  &.placeholder {
                    color: #999;
                  }
                }

                .input-arrow {
                  .arrow-icon {
                    font-size: 32rpx;
                    color: #999;
                    font-weight: 300;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  /* 时间选择区域样式 */
  .time-range-container {
    position: relative;

    .time-field {
      margin-bottom: 24rpx;

      .time-label {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;

        .label-text {
          font-size: 26rpx;
          color: #666;
        }

        .label-required {
          font-size: 24rpx;
          color: #ff4757;
          margin-left: 4rpx;
        }
      }
    }

    .time-connector {
      position: relative;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 16rpx 0;

      .connector-line {
        width: 2rpx;
        height: 24rpx;
        background: linear-gradient(to bottom, #667eea, #764ba2);
        border-radius: 1rpx;
      }

      .connector-dot {
        position: absolute;
        width: 12rpx;
        height: 12rpx;
        background: #667eea;
        border-radius: 50%;
        border: 3rpx solid #fff;
        box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
      }
    }
  }

  /* 时长显示样式 */
  .duration-display {
    margin-top: 24rpx;

    .duration-card {
      background: linear-gradient(135deg, #667eea, #764ba2);
      border-radius: 16rpx;
      padding: 20rpx 24rpx;
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      .duration-icon {
        font-size: 32rpx;
        margin-right: 16rpx;
      }

      .duration-info {
        .duration-text {
          font-size: 24rpx;
          color: rgba(255,255,255,0.8);
          display: block;
          margin-bottom: 4rpx;
        }

        .duration-value {
          font-size: 32rpx;
          font-weight: 700;
          color: #fff;
          display: block;
        }
      }
    }

    /* 详细信息样式 */
    .duration-details {
      background: rgba(102, 126, 234, 0.1);
      border-radius: 12rpx;
      padding: 16rpx 20rpx;

      .detail-item {
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .detail-label {
          font-size: 24rpx;
          color: #666;
          width: 140rpx;
          flex-shrink: 0;
        }

        .detail-value {
          font-size: 24rpx;
          color: #333;
          flex: 1;
        }
      }
    }
  }

  /* 移动端优化的上传区域样式 */
  .upload-area {
    .custom-upload-container {
      .upload-button-wrapper {
        background: #f8f9fc;
        border: 2rpx dashed #e1e8ed;
        border-radius: 16rpx;
        padding: 32rpx 24rpx;
        transition: all 0.3s ease;
        margin-bottom: 24rpx;

        &:active {
          background: #f0f4ff;
          border-color: #667eea;
          transform: scale(0.98);
        }

        .upload-button-content {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 20rpx;

          .upload-icon-large {
            width: 80rpx;
            height: 80rpx;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 16rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            .icon-plus {
              font-size: 36rpx;
              color: #fff;
              font-weight: 300;
              line-height: 1;
            }
          }

          .upload-text-content {
            flex: 1;
            text-align: left;

            .upload-main-text {
              font-size: 30rpx;
              color: #333;
              font-weight: 500;
              display: block;
              margin-bottom: 6rpx;
            }

            .upload-sub-text {
              font-size: 24rpx;
              color: #999;
              display: block;
            }
          }
        }
      }

      /* 已上传文件列表 */
      .uploaded-files {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;

        .file-item {
          position: relative;
          width: 120rpx;
          height: 120rpx;

          .file-preview {
            position: relative;
            width: 100%;
            height: 100%;
            border-radius: 12rpx;
            overflow: hidden;
            background: #f5f5f5;

            .file-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            .file-remove {
              position: absolute;
              top: -8rpx;
              right: -8rpx;
              width: 32rpx;
              height: 32rpx;
              background: #ff4757;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);

              .remove-icon {
                font-size: 20rpx;
                color: #fff;
                font-weight: bold;
                line-height: 1;
              }
            }
          }
        }
      }
    }

    /* 隐藏原生文件选择器 */
    .hidden-file-picker {
      display: none !important;
      opacity: 0;
      position: absolute;
      left: -9999rpx;
      top: -9999rpx;
    }
  }

  /* 提交按钮区域 */
  .submit-section {
    padding: 0 24rpx;

    .modern-submit-button {
      width: 100%;
      height: 96rpx;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 48rpx;
      border: none;
      box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
      transition: all 0.3s ease;

      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
      }

      .button-content {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;

        .button-text {
          font-size: 32rpx;
          font-weight: 600;
          color: #fff;
          margin-right: 12rpx;
        }

        .button-icon {
          font-size: 28rpx;
          color: #fff;
          font-weight: 600;
        }
      }
    }
  }
}

/* uni-forms-item 样式优化 */
:deep(.uni-forms-item) {
  margin-bottom: 24rpx;
  
  .uni-forms-item__label {
    font-size: 28rpx;
    color: #333;
  }
  
  .uni-forms-item__content {
    min-height: 70rpx;
  }
}

/* 日期选择器样式优化 */
:deep(.uni-datetime-picker) {
  .uni-datetime-picker-btn {
    padding: 0 20rpx;
    height: 70rpx;
    background-color: #f8f9fc;
    border-radius: 8rpx;

    .uni-datetime-picker-text {
      font-size: 28rpx;
      color: #333;
    }
  }
}

/* 当选择器打开时，防止页面滚动 */
.picker-visible {
  overflow: hidden !important;

  .content {
    overflow: hidden !important;
  }
}

/* 移动端文件上传优化样式 */
:deep(.uni-file-picker) {
  /* 隐藏原生组件的默认样式 */
  .uni-file-picker__container {
    display: none !important;
  }
}

/* 文件上传项特殊样式 */
.file-upload-item {
  :deep(.uni-forms-item__content) {
    padding: 16rpx 0;
  }
}
</style>
